from dataclasses import dataclass, field, asdict
from typing import List, Dict, Optional, Any
from datetime import datetime
import json

@dataclass
class User:
    """User model representing a system user"""
    user_id: str
    username: str
    email: str
    is_admin: bool = False
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    last_login: str = field(default_factory=lambda: datetime.now().isoformat())

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage"""
        return asdict(self)

@dataclass
class Document:
    """Document model representing a PDF document and its extracted text"""
    file_name: str
    user_id: str
    last_used: str = field(default_factory=lambda: datetime.now().isoformat())
    extraction_status: str = "not_extracted"  # not_extracted, in_progress, completed, error
    extracted_text: str = ""
    extraction_date: str = ""
    upload_time: str = field(default_factory=lambda: datetime.now().isoformat())
    file_size: int = 0
    file_path: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage"""
        return asdict(self)

@dataclass
class TemplateField:
    """Field in a document template"""
    name: str
    type: str = "text"  # text, number, date, etc.
    description: str = ""
    required: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage"""
        return asdict(self)

@dataclass
class Template:
    """Template model for document extraction"""
    template_id: str
    name: str
    description: str
    user_id: str
    fields: List[TemplateField] = field(default_factory=list)
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    updated_at: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage"""
        template_dict = asdict(self)
        template_dict['fields'] = [field.to_dict() for field in self.fields]
        return template_dict

@dataclass
class ExtractedData:
    """Data extracted from a document using a template"""
    document_id: str
    template_id: str
    user_id: str
    extracted_values: Dict[str, str] = field(default_factory=dict)
    extraction_date: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage"""
        return asdict(self)
