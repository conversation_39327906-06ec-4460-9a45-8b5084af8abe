from flask import Flask, request, jsonify, render_template, send_file, send_from_directory, url_for
import os
import json
import csv
import io
import shutil
from pathlib import Path
from werkzeug.utils import secure_filename
import time
from datetime import datetime
import random
import re
import uuid

# Import our data models and access layer
from models import User, Document, Template, Template<PERSON>ield, ExtractedData
from data_factory import DataAccessFactory
from api_routes import api

# Mock S3 service for development
class MockS3Service:
    def __init__(self):
        self._storage = {}  # Dictionary to simulate S3 storage: {user_id: {filename: content}}

    def upload_file(self, user_id, file_obj, filename):
        """Simulate uploading a file to S3."""
        if user_id not in self._storage:
            self._storage[user_id] = {}

        # Store file metadata
        self._storage[user_id][filename] = {
            'content': file_obj.read(),
            'upload_time': datetime.now().isoformat(),
            'size': len(file_obj.read()) if hasattr(file_obj, 'read') else 0
        }
        return f"s3://{user_id}/{filename}"

    def download_file(self, user_id, filename):
        """Simulate downloading a file from S3."""
        if user_id in self._storage and filename in self._storage[user_id]:
            return io.BytesIO(self._storage[user_id][filename]['content'])
        return None

    def list_files(self, user_id):
        """List all files for a user."""
        if user_id in self._storage:
            return [
                {
                    'filename': filename,
                    'upload_time': metadata['upload_time'],
                    'size': metadata['size']
                }
                for filename, metadata in self._storage[user_id].items()
            ]
        return []

    def delete_file(self, user_id, filename):
        """Delete a file from S3."""
        if user_id in self._storage and filename in self._storage[user_id]:
            del self._storage[user_id][filename]
            return True
        return False

# Initialize Flask app and mock S3
app = Flask(__name__)
mock_s3 = MockS3Service()

# Register API blueprint
app.register_blueprint(api, url_prefix='/api')

# For demo purposes, use a dummy user ID
DUMMY_USER_ID = "user123"

UPLOAD_FOLDER = 'uploads'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['ALLOWED_EXTENSIONS'] = {'pdf'}
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16 MB max upload size
app.config['DOCUMENT_TRACKING_CSV'] = 'document_tracking.csv'

# Initialize data access factory
data_factory = DataAccessFactory({
    'user_csv_path': 'users.csv',
    'document_csv_path': app.config['DOCUMENT_TRACKING_CSV'],
    'template_json_path': 'static/templates/templates.json',
    'extracted_data_csv_path': 'extracted_data.csv'
})

# Create repositories
user_repo = data_factory.get_user_repository()
document_repo = data_factory.get_document_repository()
template_repo = data_factory.get_template_repository()
extracted_data_repo = data_factory.get_extracted_data_repository()

# Set repositories in API blueprint
from api_routes import user_repo as api_user_repo, document_repo as api_document_repo, \
    template_repo as api_template_repo, extracted_data_repo as api_extracted_data_repo

api_user_repo = user_repo
api_document_repo = document_repo
api_template_repo = template_repo
api_extracted_data_repo = extracted_data_repo

# Create admin user if it doesn't exist
if not user_repo.get_user(DUMMY_USER_ID):
    admin_user = User(
        user_id=DUMMY_USER_ID,
        username="admin",
        email="<EMAIL>",
        is_admin=True
    )
    user_repo.create_user(admin_user)

# Create static/templates directory if it doesn't exist
templates_dir = os.path.join('static', 'templates')
os.makedirs(templates_dir, exist_ok=True)

# Create the template CSV file
template_path = os.path.join(templates_dir, 'auto_insurance_template.csv')
with open(template_path, 'w') as f:
    f.write('policy_number,customer_name,vehicle_make,vehicle_model,coverage_amount')

# Make sure static files are served
@app.route('/static/<path:path>')
def serve_static(path):
    return send_from_directory('static', path)

# Ensure sample PDF exists
sample_pdf_path = os.path.join(UPLOAD_FOLDER, 'sample-doc.pdf')

# Add route to serve sample PDF
@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """Serve uploaded files."""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

# Available form templates
FORM_TEMPLATES = {
    "auto_insurance": ["policy_number", "customer_name", "vehicle_make", "vehicle_model", "coverage_amount"],
    "home_insurance": ["policy_number", "customer_name", "property_address", "coverage_amount", "deductible"],
    "life_insurance": ["policy_number", "customer_name", "beneficiary", "coverage_amount", "term_length"]
}

# Directory for storing templates
TEMPLATE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'templates')
os.makedirs(TEMPLATE_DIR, exist_ok=True)
TEMPLATE_FILE = os.path.join(TEMPLATE_DIR, 'templates.json')

def read_template_files():
    """Read all CSV template files from the templates directory."""
    templates = []

    # Get all CSV files in the templates directory
    csv_files = [f for f in os.listdir(TEMPLATE_DIR) if f.endswith('_template.csv')]

    for csv_file in csv_files:
        template_type = csv_file.replace('_template.csv', '')
        csv_path = os.path.join(TEMPLATE_DIR, csv_file)

        with open(csv_path, 'r') as f:
            csv_reader = csv.DictReader(f)
            fields = []
            for row in csv_reader:
                fields.append({
                    'name': row['field_name'],
                    'type': row['data_type'],
                    'description': row['description']
                })

            templates.append({
                'type': template_type,
                'name': template_type.replace('_', ' ').title(),
                'fields': fields
            })

    return templates

# Initialize templates file if it doesn't exist
if not os.path.exists(TEMPLATE_FILE):
    # Read existing CSV templates
    templates = read_template_files()

    # Convert to the format used in templates.json
    template_definitions = []
    for i, template in enumerate(templates, 1):
        template_definitions.append({
            'id': i,
            'name': template['name'],
            'description': f"For extracting {template['name'].lower()} policy details",
            'fields': [field['name'] for field in template['fields']]  # Just use field names for backward compatibility
        })

    # Save to templates.json
    with open(TEMPLATE_FILE, 'w') as f:
        json.dump(template_definitions, f)

# Add this route for the template manager page
@app.route('/template-manager')
def template_manager():
    return render_template('template-manager.html')

# API endpoints for template management
@app.route('/api/templates', methods=['GET'])
def get_templates():
    with open(TEMPLATE_FILE, 'r') as f:
        templates = json.load(f)
    return jsonify(templates)

@app.route('/api/templates', methods=['POST'])
def create_template():
    template_data = request.json

    with open(TEMPLATE_FILE, 'r') as f:
        templates = json.load(f)

    # Generate new ID
    new_id = max([t['id'] for t in templates], default=0) + 1

    # Create new template
    new_template = {
        'id': new_id,
        'name': template_data['name'],
        'description': template_data['description'],
        'fields': template_data['fields']
    }

    templates.append(new_template)

    with open(TEMPLATE_FILE, 'w') as f:
        json.dump(templates, f)

    return jsonify(new_template), 201

@app.route('/api/templates/<int:template_id>', methods=['PUT'])
def update_template(template_id):
    template_data = request.json

    with open(TEMPLATE_FILE, 'r') as f:
        templates = json.load(f)

    # Find and update template
    for i, template in enumerate(templates):
        if template['id'] == template_id:
            templates[i] = {
                'id': template_id,
                'name': template_data['name'],
                'description': template_data['description'],
                'fields': template_data['fields']
            }
            break
    else:
        return jsonify({"error": "Template not found"}), 404

    with open(TEMPLATE_FILE, 'w') as f:
        json.dump(templates, f)

    return jsonify(templates[i])

@app.route('/api/templates/<int:template_id>', methods=['DELETE'])
def delete_template(template_id):
    with open(TEMPLATE_FILE, 'r') as f:
        templates = json.load(f)

    # Find and remove template
    templates = [t for t in templates if t['id'] != template_id]

    with open(TEMPLATE_FILE, 'w') as f:
        json.dump(templates, f)

    return '', 204

@app.route('/')
def index():
    return render_template('index.html', templates=FORM_TEMPLATES)

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'files[]' not in request.files:
        return jsonify({'error': 'No file part'}), 400

    files = request.files.getlist('files[]')

    if not files or files[0].filename == '':
        return jsonify({'error': 'No selected file'}), 400

    upload_results = []

    for file in files:
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

            # Save file directly to the uploads folder
            file.save(filepath)

            # Generate a timestamp for the upload
            upload_time = datetime.now().isoformat()

            # Get file size
            file_size = os.path.getsize(filepath)

            # Create a document record
            document = Document(
                file_name=filename,
                user_id=DUMMY_USER_ID,
                last_used=upload_time,
                extraction_status='not_extracted',
                extracted_text='',
                upload_time=upload_time,
                file_size=file_size,
                file_path=filepath
            )

            # Save to repository
            try:
                document_repo.create_document(document)
            except ValueError:
                # Document already exists, update it
                existing_doc = document_repo.get_document(filename)
                existing_doc.last_used = upload_time
                existing_doc.file_size = file_size
                existing_doc.file_path = filepath
                document_repo.update_document(existing_doc)

            # Add to results
            upload_results.append({
                'name': filename,
                'url': f"/serve_file/{filename}",
                'upload_time': upload_time,
                'extracted_text': ''
            })
        else:
            return jsonify({
                'error': f'Invalid file type for {file.filename}. Only PDF files are allowed.'
            }), 400

    return jsonify({'success': True, 'files': upload_results})

@app.route('/serve_file/<filename>')
def serve_file(filename):
    """Serve a file from the uploads directory"""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/extract_text', methods=['POST'])
def extract_text():
    """Extract text from a PDF file"""
    data = request.json
    file_path = data.get('file_path')

    if not file_path:
        return jsonify({'error': 'No file path provided'}), 400

    try:
        # In a real application, you would use a PDF extraction library here
        # For this demo, we'll just return dummy text
        extraction_time = datetime.now().isoformat()
        extracted_text = f"This is extracted text from {file_path}.\n\nIt contains multiple lines of text that would normally be extracted from a PDF file.\n\nIn a real application, this would be actual content from the PDF document.\n\nPolicy Number: POL-{random.randint(1000, 9999)}-{random.randint(1000, 9999)}\nCustomer Name: John Doe\nVehicle Make: Toyota\nVehicle Model: Camry\nCoverage Amount: $50,000"

        # Update the document using our repository
        document = document_repo.get_document(file_path)

        if document:
            # Update existing document
            document.extraction_status = 'completed'
            document.extracted_text = extracted_text
            document.last_used = extraction_time
            document.extraction_date = extraction_time
            document_repo.update_document(document)
        else:
            # Create new document
            document = Document(
                file_name=file_path,
                user_id=DUMMY_USER_ID,
                last_used=extraction_time,
                extraction_status='completed',
                extracted_text=extracted_text,
                extraction_date=extraction_time
            )
            document_repo.create_document(document)

        # Simulate processing time
        time.sleep(1)

        return jsonify({
            'success': True,
            'file_path': file_path,
            'extracted_text': extracted_text
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/list_files', methods=['GET'])
def list_files():
    """List all PDF files in the uploads directory"""
    try:
        # Get user_id from query parameter or use default
        user_id = request.args.get('user_id', DUMMY_USER_ID)

        files = []
        for filename in os.listdir(app.config['UPLOAD_FOLDER']):
            if filename.endswith('.pdf'):
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                # Get file stats
                stats = os.stat(file_path)
                # Use creation time as upload time if we don't have tracking data
                creation_time = datetime.fromtimestamp(stats.st_ctime).isoformat()

                # Check if we have tracking data for this file
                document = document_repo.get_document(filename)

                if document:
                    files.append({
                        'filename': filename,
                        'upload_time': document.last_used,
                        'size': stats.st_size,
                        'extraction_status': document.extraction_status
                    })
                else:
                    # Create a new document record
                    new_doc = Document(
                        file_name=filename,
                        user_id=user_id,
                        last_used=creation_time,
                        upload_time=creation_time,
                        file_size=stats.st_size,
                        file_path=file_path
                    )
                    document_repo.create_document(new_doc)

                    files.append({
                        'filename': filename,
                        'upload_time': creation_time,
                        'size': stats.st_size,
                        'extraction_status': 'not_extracted'
                    })

        return jsonify({'files': files})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/get_document_tracking', methods=['GET'])
def get_document_tracking():
    """Get all document tracking data"""
    try:
        # Get user_id from query parameter or use default
        user_id = request.args.get('user_id', DUMMY_USER_ID)

        # Get all documents for the user
        documents = document_repo.list_documents(user_id)

        # Convert to dictionary for JSON response
        document_dicts = [doc.to_dict() for doc in documents]

        return jsonify({'documents': document_dicts})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/update_document_tracking', methods=['POST'])
def update_document_tracking():
    """Update document tracking data"""
    data = request.json
    file_name = data.get('file_name')
    status = data.get('status')
    extracted_text = data.get('extracted_text', '')

    if not file_name:
        return jsonify({'error': 'No file name provided'}), 400

    try:
        # Get existing document or create a new one
        document = document_repo.get_document(file_name)

        if document:
            # Update existing document
            document.extraction_status = status
            if extracted_text:
                document.extracted_text = extracted_text
            document.last_used = datetime.now().isoformat()
            if status == 'completed' or status == 'error':
                document.extraction_date = datetime.now().isoformat()

            document_repo.update_document(document)
        else:
            # Create new document
            document = Document(
                file_name=file_name,
                user_id=DUMMY_USER_ID,
                last_used=datetime.now().isoformat(),
                extraction_status=status,
                extracted_text=extracted_text,
                extraction_date=datetime.now().isoformat() if status == 'completed' or status == 'error' else ''
            )
            document_repo.create_document(document)

        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Helper function to update the document tracking CSV
def update_document_tracking_csv(file_name, last_used, status, extracted_text):
    """Update or add an entry to the document tracking CSV"""
    csv_path = app.config['DOCUMENT_TRACKING_CSV']
    rows = []
    file_exists = False

    # Define expected fieldnames for our CSV
    fieldnames = ['file_name', 'last_used', 'extraction_status', 'extracted_text', 'extraction_date']

    # Ensure the CSV exists
    ensure_tracking_csv_exists()

    # Check if file exists and read existing data
    if os.path.exists(csv_path) and os.path.getsize(csv_path) > 0:
        try:
            with open(csv_path, 'r', newline='') as csvfile:
                reader = csv.DictReader(csvfile)

                # Get fieldnames from the file or use our expected fieldnames
                file_fieldnames = reader.fieldnames if reader.fieldnames else fieldnames

                for row in reader:
                    # Create a sanitized row with only the expected fields
                    sanitized_row = {}
                    for field in fieldnames:
                        sanitized_row[field] = row.get(field, '')

                    if row.get('file_name') == file_name or row.get('file_name') == f"uploads/{file_name}":
                        # Update existing entry
                        file_exists = True
                        sanitized_row['last_used'] = last_used
                        if status:
                            sanitized_row['extraction_status'] = status
                        if extracted_text:
                            sanitized_row['extracted_text'] = extracted_text
                        if status == 'completed' or status == 'error':
                            sanitized_row['extraction_date'] = datetime.now().isoformat()

                    rows.append(sanitized_row)
        except Exception as e:
            print(f"Error reading CSV file: {e}")
            # If there's an error reading the file, we'll start fresh
            rows = []

    # Add new entry if file doesn't exist
    if not file_exists:
        rows.append({
            'file_name': file_name,
            'last_used': last_used,
            'extraction_status': status,
            'extracted_text': extracted_text,
            'extraction_date': datetime.now().isoformat() if status == 'completed' or status == 'error' else ''
        })

    # Write back to CSV
    try:
        with open(csv_path, 'w', newline='') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(rows)
    except Exception as e:
        print(f"Error writing to CSV file: {e}")
        # Create a new CSV file if writing fails
        with open(csv_path, 'w', newline='') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            # Only write the new entry to avoid issues with existing data
            if not file_exists:
                writer.writerow({
                    'file_name': file_name,
                    'last_used': last_used,
                    'extraction_status': status,
                    'extracted_text': extracted_text,
                    'extraction_date': datetime.now().isoformat() if status == 'completed' or status == 'error' else ''
                })

# Helper function to get document tracking data for a specific file
def get_document_tracking_data(file_name):
    """Get tracking data for a specific file"""
    csv_path = app.config['DOCUMENT_TRACKING_CSV']

    if os.path.exists(csv_path):
        with open(csv_path, 'r', newline='') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                if row['file_name'] == file_name or row['file_name'] == f"uploads/{file_name}":
                    return row

    return None

@app.route('/generate_answers', methods=['POST'])
def generate_answers():
    # In a real application, this would use a more sophisticated algorithm
    # or machine learning model to extract information from the text.
    data = request.json
    extracted_text = data.get('extracted_text', '')
    fields = data.get('fields', [])
    form_type = data.get('form_type', 'auto_insurance')

    results = []
    result = {}

    # Extract policy number (pattern: POL-XXXX-XXXX)
    policy_match = re.search(r'Policy Number: (POL-\d{4}-\d{4})', extracted_text)
    if policy_match and 'policy_number' in fields:
        result['policy_number'] = policy_match.group(1)
    else:
        result['policy_number'] = "POL-" + str(random.randint(1000, 9999)) + "-" + str(random.randint(1000, 9999))

    # Extract customer name (pattern: Customer Name: XXX)
    name_match = re.search(r'Customer Name: ([^\n]+)', extracted_text)
    if name_match and 'customer_name' in fields:
        result['customer_name'] = name_match.group(1)
    else:
        result['customer_name'] = "John Doe"

    # Extract different fields based on form type
    if form_type == 'auto_insurance':
        # Vehicle make
        make_match = re.search(r'Vehicle Make: ([^\n]+)', extracted_text)
        if make_match and 'vehicle_make' in fields:
            result['vehicle_make'] = make_match.group(1)
        else:
            result['vehicle_make'] = "Toyota"

        # Vehicle model
        model_match = re.search(r'Vehicle Model: ([^\n]+)', extracted_text)
        if model_match and 'vehicle_model' in fields:
            result['vehicle_model'] = model_match.group(1)
        else:
            result['vehicle_model'] = "Camry"

        # Coverage amount
        coverage_match = re.search(r'Coverage Amount: ([^\n]+)', extracted_text)
        if coverage_match and 'coverage_amount' in fields:
            result['coverage_amount'] = coverage_match.group(1)
        else:
            result['coverage_amount'] = "$50,000"

    elif form_type == 'home_insurance':
        # Property address
        address_match = re.search(r'Property Address: ([^\n]+)', extracted_text)
        if address_match and 'property_address' in fields:
            result['property_address'] = address_match.group(1)
        else:
            result['property_address'] = "123 Main St, Anytown, USA"

        # Coverage amount
        coverage_match = re.search(r'Coverage Amount: ([^\n]+)', extracted_text)
        if coverage_match and 'coverage_amount' in fields:
            result['coverage_amount'] = coverage_match.group(1)
        else:
            result['coverage_amount'] = "$250,000"

        # Deductible
        deductible_match = re.search(r'Deductible: ([^\n]+)', extracted_text)
        if deductible_match and 'deductible' in fields:
            result['deductible'] = deductible_match.group(1)
        else:
            result['deductible'] = "$1,000"

    elif form_type == 'life_insurance':
        # Beneficiary
        beneficiary_match = re.search(r'Beneficiary: ([^\n]+)', extracted_text)
        if beneficiary_match and 'beneficiary' in fields:
            result['beneficiary'] = beneficiary_match.group(1)
        else:
            result['beneficiary'] = "Jane Doe"

        # Coverage amount
        coverage_match = re.search(r'Coverage Amount: ([^\n]+)', extracted_text)
        if coverage_match and 'coverage_amount' in fields:
            result['coverage_amount'] = coverage_match.group(1)
        else:
            result['coverage_amount'] = "$500,000"

        # Term length
        term_match = re.search(r'Term Length: ([^\n]+)', extracted_text)
        if term_match and 'term_length' in fields:
            result['term_length'] = term_match.group(1)
        else:
            result['term_length'] = "30 years"

    results.append(result)

    # Return with the fabricated prompt (for demo purposes)
    return jsonify({
        'extracted_data': results,
        'prompt': f"Extract the following fields from the text: {fields}\n\nText: {extracted_text[:100]}..."
    })

@app.route('/export', methods=['POST'])
def export_data():
    data = request.json
    template_type = data.get('template_type')
    extracted_data = data.get('extracted_data', [])

    if not template_type or not extracted_data:
        return jsonify({'error': 'Missing template type or data'}), 400

    # Get field names from the first entry
    if len(extracted_data) > 0:
        fieldnames = extracted_data[0].keys()
    else:
        return jsonify({'error': 'No data to export'}), 400

    # Generate CSV
    output = io.StringIO()
    writer = csv.DictWriter(output, fieldnames=fieldnames)
    writer.writeheader()
    for row in extracted_data:
        writer.writerow(row)

    csv_data = output.getvalue()

    # In a real application, you would save this to a file or cloud storage
    # and return a download link

    from flask import Response
    return Response(
        csv_data,
        mimetype="text/csv",
        headers={"Content-disposition": f"attachment; filename={template_type}_export.csv"}
    )

def format_prompt(form_type, form_answers):
    """Format the prompt based on form type and current answers."""
    prompts = {
        "auto_insurance": """
        Extract information from the following insurance document.
        Document Type: Auto Insurance
        Current Information:
        {answers}

        Please extract the following fields:
        - Policy Number
        - Customer Name
        - Vehicle Make
        - Vehicle Model
        - Coverage Amount

        Document Text:
        {text}
        """,
        "home_insurance": """
        Extract information from the following insurance document.
        Document Type: Home Insurance
        Current Information:
        {answers}

        Please extract the following fields:
        - Policy Number
        - Customer Name
        - Property Address
        - Coverage Amount
        - Deductible

        Document Text:
        {text}
        """,
        "life_insurance": """
        Extract information from the following insurance document.
        Document Type: Life Insurance
        Current Information:
        {answers}

        Please extract the following fields:
        - Policy Number
        - Customer Name
        - Beneficiary
        - Coverage Amount
        - Term Length

        Document Text:
        {text}
        """
    }

    # Convert form_answers dict to a formatted string
    answers_str = "\n".join([f"- {k}: {v}" for k, v in form_answers.items()])

    # Get the appropriate prompt template
    prompt_template = prompts.get(form_type, prompts[form_type])

    # Format the prompt with the answers and text
    return prompt_template.format(answers=answers_str, text="{text}")

def invoke_model(prompt):
    """Dummy function that would normally call an LLM API."""
    # In a real implementation, this would call an actual LLM API
    return prompt

def determine_template_type(fields):
    """Determine which template type matches the given fields."""
    for template_type, template_fields in FORM_TEMPLATES.items():
        if set(fields) == set(template_fields):
            return template_type

    # Default to auto_insurance if no match
    return "auto_insurance"

# Dummy function to simulate PDF text extraction
def extract_text_from_pdfs(file_paths):
    # In a real app, this would use a PDF parsing library
    dummy_text = """
    # Insurance Policy Document

    **Policy Number**: POL-12345
    **Customer Name**: John Smith
    **Vehicle Make**: Toyota
    **Vehicle Model**: Camry
    **Coverage Amount**: $50,000

    This policy is effective from January 1, 2023.
    """
    return dummy_text

# Dummy function to simulate LLM processing
def process_with_llm(text, template_type):
    # In a real app, this would call an actual LLM API
    dummy_responses = {
        "auto_insurance": [
            {
                "policy_number": "POL-12345",
                "customer_name": "John Smith",
                "vehicle_make": "Toyota",
                "vehicle_model": "Camry",
                "coverage_amount": "$50,000"
            }
        ],
        "home_insurance": [
            {
                "policy_number": "POL-67890",
                "customer_name": "Jane Doe",
                "property_address": "123 Main St",
                "coverage_amount": "$300,000",
                "deductible": "$1,000"
            }
        ],
        "life_insurance": [
            {
                "policy_number": "POL-24680",
                "customer_name": "Bob Johnson",
                "beneficiary": "Sarah Johnson",
                "coverage_amount": "$500,000",
                "term_length": "30 years"
            }
        ]
    }

    return dummy_responses.get(template_type, [])

# Modify the get_template_samples endpoint to use read_template_files
@app.route('/api/templates/samples', methods=['GET'])
def get_template_samples():
    """Get all available templates with their schema information."""
    templates = read_template_files()

    # Read the templates.json file to get additional metadata
    with open(TEMPLATE_FILE, 'r') as f:
        template_definitions = json.load(f)

    # Combine the data
    for template in templates:
        # Find matching template definition
        for definition in template_definitions:
            if definition['name'].lower().replace(' ', '_') == template['type']:
                template['id'] = definition['id']
                template['description'] = definition['description']
                break

    return jsonify(templates)

def ensure_tracking_csv_exists():
    csv_path = app.config['DOCUMENT_TRACKING_CSV']
    if not os.path.exists(csv_path):
        with open(csv_path, 'w', newline='') as csvfile:
            fieldnames = ['file_name', 'last_used', 'extraction_status', 'extracted_text', 'extraction_date']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

if __name__ == '__main__':
    app.run(debug=True, port=5001)