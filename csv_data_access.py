import os
import csv
import json
from typing import List, Dict, Optional, Any, Union
from datetime import datetime
import uuid

from data_access import UserRepository, DocumentRepository, TemplateRepository, ExtractedDataRepository
from models import User, Document, Template, TemplateField, ExtractedData

class CSVUserRepository(UserRepository):
    """CSV implementation of UserRepository"""
    
    def __init__(self, csv_path: str = "users.csv"):
        self.csv_path = csv_path
        self._ensure_csv_exists()
    
    def _ensure_csv_exists(self):
        """Ensure the CSV file exists with proper headers"""
        if not os.path.exists(self.csv_path):
            with open(self.csv_path, 'w', newline='') as csvfile:
                fieldnames = ['user_id', 'username', 'email', 'is_admin', 'created_at', 'last_login']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
    
    def create_user(self, user: User) -> User:
        """Create a new user"""
        users = self.list_users()
        
        # Check if user already exists
        for existing_user in users:
            if existing_user.user_id == user.user_id:
                raise ValueError(f"User with ID {user.user_id} already exists")
        
        # Add the new user
        users.append(user)
        
        # Write back to CSV
        self._write_users(users)
        
        return user
    
    def get_user(self, user_id: str) -> Optional[User]:
        """Get a user by ID"""
        users = self.list_users()
        
        for user in users:
            if user.user_id == user_id:
                return user
        
        return None
    
    def update_user(self, user: User) -> User:
        """Update a user"""
        users = self.list_users()
        updated = False
        
        for i, existing_user in enumerate(users):
            if existing_user.user_id == user.user_id:
                users[i] = user
                updated = True
                break
        
        if not updated:
            raise ValueError(f"User with ID {user.user_id} not found")
        
        # Write back to CSV
        self._write_users(users)
        
        return user
    
    def delete_user(self, user_id: str) -> bool:
        """Delete a user"""
        users = self.list_users()
        initial_count = len(users)
        
        users = [user for user in users if user.user_id != user_id]
        
        if len(users) == initial_count:
            return False  # No user was deleted
        
        # Write back to CSV
        self._write_users(users)
        
        return True
    
    def list_users(self) -> List[User]:
        """List all users"""
        if not os.path.exists(self.csv_path) or os.path.getsize(self.csv_path) == 0:
            return []
        
        users = []
        with open(self.csv_path, 'r', newline='') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                # Convert is_admin from string to boolean
                is_admin = row['is_admin'].lower() == 'true'
                
                user = User(
                    user_id=row['user_id'],
                    username=row['username'],
                    email=row['email'],
                    is_admin=is_admin,
                    created_at=row['created_at'],
                    last_login=row['last_login']
                )
                users.append(user)
        
        return users
    
    def _write_users(self, users: List[User]):
        """Write users to CSV file"""
        with open(self.csv_path, 'w', newline='') as csvfile:
            fieldnames = ['user_id', 'username', 'email', 'is_admin', 'created_at', 'last_login']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for user in users:
                writer.writerow(user.to_dict())

class CSVDocumentRepository(DocumentRepository):
    """CSV implementation of DocumentRepository"""
    
    def __init__(self, csv_path: str = "document_tracking.csv"):
        self.csv_path = csv_path
        self._ensure_csv_exists()
    
    def _ensure_csv_exists(self):
        """Ensure the CSV file exists with proper headers"""
        if not os.path.exists(self.csv_path):
            with open(self.csv_path, 'w', newline='') as csvfile:
                fieldnames = ['file_name', 'user_id', 'last_used', 'extraction_status', 
                             'extracted_text', 'extraction_date', 'upload_time', 'file_size', 'file_path']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
    
    def create_document(self, document: Document) -> Document:
        """Create a new document record"""
        documents = self.list_documents()
        
        # Check if document already exists
        for existing_doc in documents:
            if existing_doc.file_name == document.file_name:
                raise ValueError(f"Document with name {document.file_name} already exists")
        
        # Add the new document
        documents.append(document)
        
        # Write back to CSV
        self._write_documents(documents)
        
        return document
    
    def get_document(self, file_name: str) -> Optional[Document]:
        """Get a document by file name"""
        documents = self.list_documents()
        
        for doc in documents:
            if doc.file_name == file_name:
                return doc
        
        return None
    
    def update_document(self, document: Document) -> Document:
        """Update a document"""
        documents = self.list_documents()
        updated = False
        
        for i, existing_doc in enumerate(documents):
            if existing_doc.file_name == document.file_name:
                documents[i] = document
                updated = True
                break
        
        if not updated:
            raise ValueError(f"Document with name {document.file_name} not found")
        
        # Write back to CSV
        self._write_documents(documents)
        
        return document
    
    def delete_document(self, file_name: str) -> bool:
        """Delete a document"""
        documents = self.list_documents()
        initial_count = len(documents)
        
        documents = [doc for doc in documents if doc.file_name != file_name]
        
        if len(documents) == initial_count:
            return False  # No document was deleted
        
        # Write back to CSV
        self._write_documents(documents)
        
        return True
    
    def list_documents(self, user_id: Optional[str] = None) -> List[Document]:
        """List all documents, optionally filtered by user_id"""
        if not os.path.exists(self.csv_path) or os.path.getsize(self.csv_path) == 0:
            return []
        
        documents = []
        with open(self.csv_path, 'r', newline='') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                # Skip if user_id filter is provided and doesn't match
                if user_id and row.get('user_id', '') != user_id:
                    continue
                
                # Handle missing fields with defaults
                doc = Document(
                    file_name=row['file_name'],
                    user_id=row.get('user_id', ''),
                    last_used=row.get('last_used', datetime.now().isoformat()),
                    extraction_status=row.get('extraction_status', 'not_extracted'),
                    extracted_text=row.get('extracted_text', ''),
                    extraction_date=row.get('extraction_date', ''),
                    upload_time=row.get('upload_time', datetime.now().isoformat()),
                    file_size=int(row.get('file_size', 0)),
                    file_path=row.get('file_path', '')
                )
                documents.append(doc)
        
        return documents
    
    def update_extraction_status(self, file_name: str, status: str, extracted_text: str = "") -> Document:
        """Update the extraction status and text of a document"""
        document = self.get_document(file_name)
        
        if not document:
            raise ValueError(f"Document with name {file_name} not found")
        
        document.extraction_status = status
        if extracted_text:
            document.extracted_text = extracted_text
        
        if status == 'completed' or status == 'error':
            document.extraction_date = datetime.now().isoformat()
        
        document.last_used = datetime.now().isoformat()
        
        return self.update_document(document)
    
    def _write_documents(self, documents: List[Document]):
        """Write documents to CSV file"""
        with open(self.csv_path, 'w', newline='') as csvfile:
            fieldnames = ['file_name', 'user_id', 'last_used', 'extraction_status', 
                         'extracted_text', 'extraction_date', 'upload_time', 'file_size', 'file_path']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for doc in documents:
                writer.writerow(doc.to_dict())

class JSONTemplateRepository(TemplateRepository):
    """JSON implementation of TemplateRepository"""
    
    def __init__(self, json_path: str = "static/templates/templates.json"):
        self.json_path = json_path
        self._ensure_json_exists()
    
    def _ensure_json_exists(self):
        """Ensure the JSON file exists"""
        os.makedirs(os.path.dirname(self.json_path), exist_ok=True)
        if not os.path.exists(self.json_path):
            with open(self.json_path, 'w') as f:
                json.dump([], f)
    
    def create_template(self, template: Template) -> Template:
        """Create a new template"""
        templates = self.list_templates()
        
        # Check if template already exists
        for existing_template in templates:
            if existing_template.template_id == template.template_id:
                raise ValueError(f"Template with ID {template.template_id} already exists")
        
        # Add the new template
        templates.append(template)
        
        # Write back to JSON
        self._write_templates(templates)
        
        return template
    
    def get_template(self, template_id: str) -> Optional[Template]:
        """Get a template by ID"""
        templates = self.list_templates()
        
        for template in templates:
            if template.template_id == template_id:
                return template
        
        return None
    
    def update_template(self, template: Template) -> Template:
        """Update a template"""
        templates = self.list_templates()
        updated = False
        
        for i, existing_template in enumerate(templates):
            if existing_template.template_id == template.template_id:
                templates[i] = template
                updated = True
                break
        
        if not updated:
            raise ValueError(f"Template with ID {template.template_id} not found")
        
        # Write back to JSON
        self._write_templates(templates)
        
        return template
    
    def delete_template(self, template_id: str) -> bool:
        """Delete a template"""
        templates = self.list_templates()
        initial_count = len(templates)
        
        templates = [template for template in templates if template.template_id != template_id]
        
        if len(templates) == initial_count:
            return False  # No template was deleted
        
        # Write back to JSON
        self._write_templates(templates)
        
        return True
    
    def list_templates(self, user_id: Optional[str] = None) -> List[Template]:
        """List all templates, optionally filtered by user_id"""
        if not os.path.exists(self.json_path) or os.path.getsize(self.json_path) == 0:
            return []
        
        with open(self.json_path, 'r') as f:
            data = json.load(f)
        
        templates = []
        for item in data:
            # Skip if user_id filter is provided and doesn't match
            if user_id and item.get('user_id', '') != user_id:
                continue
            
            # Convert fields from dict to TemplateField objects
            fields = []
            for field_data in item.get('fields', []):
                if isinstance(field_data, dict):
                    field = TemplateField(
                        name=field_data.get('name', ''),
                        type=field_data.get('type', 'text'),
                        description=field_data.get('description', ''),
                        required=field_data.get('required', False)
                    )
                else:
                    # Handle case where fields are just strings
                    field = TemplateField(name=field_data)
                fields.append(field)
            
            template = Template(
                template_id=str(item.get('id', '')),
                name=item.get('name', ''),
                description=item.get('description', ''),
                user_id=item.get('user_id', ''),
                fields=fields,
                created_at=item.get('created_at', datetime.now().isoformat()),
                updated_at=item.get('updated_at', datetime.now().isoformat())
            )
            templates.append(template)
        
        return templates
    
    def _write_templates(self, templates: List[Template]):
        """Write templates to JSON file"""
        data = []
        for template in templates:
            template_dict = template.to_dict()
            # Convert template_id to id for backward compatibility
            template_dict['id'] = template_dict.pop('template_id')
            data.append(template_dict)
        
        with open(self.json_path, 'w') as f:
            json.dump(data, f, indent=2)

class CSVExtractedDataRepository(ExtractedDataRepository):
    """CSV implementation of ExtractedDataRepository"""
    
    def __init__(self, csv_path: str = "extracted_data.csv"):
        self.csv_path = csv_path
        self._ensure_csv_exists()
    
    def _ensure_csv_exists(self):
        """Ensure the CSV file exists with proper headers"""
        if not os.path.exists(self.csv_path):
            with open(self.csv_path, 'w', newline='') as csvfile:
                fieldnames = ['document_id', 'template_id', 'user_id', 'extracted_values', 'extraction_date']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
    
    def create_extracted_data(self, data: ExtractedData) -> ExtractedData:
        """Create new extracted data"""
        all_data = self.list_extracted_data()
        
        # Check if data already exists
        for existing_data in all_data:
            if existing_data.document_id == data.document_id and existing_data.template_id == data.template_id:
                raise ValueError(f"Extracted data for document {data.document_id} and template {data.template_id} already exists")
        
        # Add the new data
        all_data.append(data)
        
        # Write back to CSV
        self._write_extracted_data(all_data)
        
        return data
    
    def get_extracted_data(self, document_id: str, template_id: str) -> Optional[ExtractedData]:
        """Get extracted data by document and template IDs"""
        all_data = self.list_extracted_data()
        
        for data in all_data:
            if data.document_id == document_id and data.template_id == template_id:
                return data
        
        return None
    
    def update_extracted_data(self, data: ExtractedData) -> ExtractedData:
        """Update extracted data"""
        all_data = self.list_extracted_data()
        updated = False
        
        for i, existing_data in enumerate(all_data):
            if existing_data.document_id == data.document_id and existing_data.template_id == data.template_id:
                all_data[i] = data
                updated = True
                break
        
        if not updated:
            raise ValueError(f"Extracted data for document {data.document_id} and template {data.template_id} not found")
        
        # Write back to CSV
        self._write_extracted_data(all_data)
        
        return data
    
    def delete_extracted_data(self, document_id: str, template_id: str) -> bool:
        """Delete extracted data"""
        all_data = self.list_extracted_data()
        initial_count = len(all_data)
        
        all_data = [data for data in all_data if not (data.document_id == document_id and data.template_id == template_id)]
        
        if len(all_data) == initial_count:
            return False  # No data was deleted
        
        # Write back to CSV
        self._write_extracted_data(all_data)
        
        return True
    
    def list_extracted_data(self, document_id: Optional[str] = None, template_id: Optional[str] = None) -> List[ExtractedData]:
        """List all extracted data, optionally filtered by document or template ID"""
        if not os.path.exists(self.csv_path) or os.path.getsize(self.csv_path) == 0:
            return []
        
        all_data = []
        with open(self.csv_path, 'r', newline='') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                # Skip if filters don't match
                if document_id and row['document_id'] != document_id:
                    continue
                if template_id and row['template_id'] != template_id:
                    continue
                
                # Parse extracted_values from JSON string
                try:
                    extracted_values = json.loads(row['extracted_values'])
                except (json.JSONDecodeError, TypeError):
                    extracted_values = {}
                
                data = ExtractedData(
                    document_id=row['document_id'],
                    template_id=row['template_id'],
                    user_id=row['user_id'],
                    extracted_values=extracted_values,
                    extraction_date=row['extraction_date']
                )
                all_data.append(data)
        
        return all_data
    
    def _write_extracted_data(self, all_data: List[ExtractedData]):
        """Write extracted data to CSV file"""
        with open(self.csv_path, 'w', newline='') as csvfile:
            fieldnames = ['document_id', 'template_id', 'user_id', 'extracted_values', 'extraction_date']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for data in all_data:
                row = data.to_dict()
                # Convert extracted_values dict to JSON string
                row['extracted_values'] = json.dumps(row['extracted_values'])
                writer.writerow(row)
