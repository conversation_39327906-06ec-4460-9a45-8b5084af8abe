from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Any, Union
from models import User, Document, Template, ExtractedData

class UserRepository(ABC):
    """Abstract interface for user data access"""
    
    @abstractmethod
    def create_user(self, user: User) -> User:
        """Create a new user"""
        pass
    
    @abstractmethod
    def get_user(self, user_id: str) -> Optional[User]:
        """Get a user by ID"""
        pass
    
    @abstractmethod
    def update_user(self, user: User) -> User:
        """Update a user"""
        pass
    
    @abstractmethod
    def delete_user(self, user_id: str) -> bool:
        """Delete a user"""
        pass
    
    @abstractmethod
    def list_users(self) -> List[User]:
        """List all users"""
        pass

class DocumentRepository(ABC):
    """Abstract interface for document data access"""
    
    @abstractmethod
    def create_document(self, document: Document) -> Document:
        """Create a new document record"""
        pass
    
    @abstractmethod
    def get_document(self, file_name: str) -> Optional[Document]:
        """Get a document by file name"""
        pass
    
    @abstractmethod
    def update_document(self, document: Document) -> Document:
        """Update a document"""
        pass
    
    @abstractmethod
    def delete_document(self, file_name: str) -> bool:
        """Delete a document"""
        pass
    
    @abstractmethod
    def list_documents(self, user_id: Optional[str] = None) -> List[Document]:
        """List all documents, optionally filtered by user_id"""
        pass
    
    @abstractmethod
    def update_extraction_status(self, file_name: str, status: str, extracted_text: str = "") -> Document:
        """Update the extraction status and text of a document"""
        pass

class TemplateRepository(ABC):
    """Abstract interface for template data access"""
    
    @abstractmethod
    def create_template(self, template: Template) -> Template:
        """Create a new template"""
        pass
    
    @abstractmethod
    def get_template(self, template_id: str) -> Optional[Template]:
        """Get a template by ID"""
        pass
    
    @abstractmethod
    def update_template(self, template: Template) -> Template:
        """Update a template"""
        pass
    
    @abstractmethod
    def delete_template(self, template_id: str) -> bool:
        """Delete a template"""
        pass
    
    @abstractmethod
    def list_templates(self, user_id: Optional[str] = None) -> List[Template]:
        """List all templates, optionally filtered by user_id"""
        pass

class ExtractedDataRepository(ABC):
    """Abstract interface for extracted data access"""
    
    @abstractmethod
    def create_extracted_data(self, data: ExtractedData) -> ExtractedData:
        """Create new extracted data"""
        pass
    
    @abstractmethod
    def get_extracted_data(self, document_id: str, template_id: str) -> Optional[ExtractedData]:
        """Get extracted data by document and template IDs"""
        pass
    
    @abstractmethod
    def update_extracted_data(self, data: ExtractedData) -> ExtractedData:
        """Update extracted data"""
        pass
    
    @abstractmethod
    def delete_extracted_data(self, document_id: str, template_id: str) -> bool:
        """Delete extracted data"""
        pass
    
    @abstractmethod
    def list_extracted_data(self, document_id: Optional[str] = None, template_id: Optional[str] = None) -> List[ExtractedData]:
        """List all extracted data, optionally filtered by document or template ID"""
        pass
