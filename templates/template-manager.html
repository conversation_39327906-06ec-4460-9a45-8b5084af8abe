<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template Manager - Insurance Form Extractor</title>
    <style>
        :root {
            --primary: #4361ee;
            --primary-light: #4895ef;
            --secondary: #3f37c9;
            --success: #4cc9f0;
            --danger: #f72585;
            --light: #f8f9fa;
            --dark: #212529;
            --gray: #6c757d;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        body {
            font-family: 'Segoe UI', Roboto, -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: var(--dark);
            line-height: 1.6;
        }
        
        /* Navbar styles */
        .navbar {
            background-color: var(--primary);
            color: white;
            padding: 0 24px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .navbar-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1600px;
            margin: 0 auto;
            height: 64px;
        }
        
        .navbar-brand {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.5rem;
            font-weight: 600;
            text-decoration: none;
            color: white;
        }
        
        .navbar-logo {
            height: 40px;
            width: auto;
            margin: 12px 0;
        }
        
        .navbar-nav {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        .nav-item {
            margin-left: 24px;
        }
        
        .nav-link {
            color: rgba(255, 255, 255, 0.85);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 0;
            transition: var(--transition);
            position: relative;
        }
        
        .nav-link:hover {
            color: white;
        }
        
        .nav-link.active {
            color: white;
        }
        
        .nav-link.active:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: white;
            border-radius: 3px 3px 0 0;
        }
        
        .main-container {
            min-height: calc(100vh - 64px);
            padding: 24px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 24px;
            margin-bottom: 24px;
        }
        
        h2 {
            color: var(--primary);
            font-weight: 600;
            margin-top: 0;
            padding-bottom: 12px;
            border-bottom: 1px solid #eee;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--dark);
        }
        
        input[type="file"], input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }
        
        input[type="file"] {
            border: 1px dashed var(--primary-light);
            background-color: rgba(67, 97, 238, 0.05);
        }
        
        input[type="file"]:hover {
            background-color: rgba(67, 97, 238, 0.1);
        }
        
        input[type="text"]:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
            outline: none;
        }
        
        button {
            background-color: var(--primary);
            color: white;
            padding: 12px 18px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 500;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            margin-bottom: 12px;
        }
        
        button:hover {
            background-color: var(--secondary);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }
        
        .template-list {
            margin-top: 24px;
        }
        
        .template-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border: 1px solid #eee;
            border-radius: var(--border-radius);
            margin-bottom: 12px;
            transition: var(--transition);
        }
        
        .template-item:hover {
            border-color: var(--primary-light);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .template-info h4 {
            margin: 0 0 4px 0;
            color: var(--dark);
        }
        
        .template-info p {
            margin: 0;
            color: var(--gray);
            font-size: 0.9rem;
        }
        
        .template-actions button {
            margin: 0 0 0 8px;
            padding: 8px 12px;
            font-size: 0.9rem;
        }
        
        .template-actions .edit-btn {
            background-color: var(--primary-light);
        }
        
        .template-actions .delete-btn {
            background-color: var(--danger);
        }
        
        .field-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 16px;
        }
        
        .field-item {
            background-color: rgba(67, 97, 238, 0.1);
            color: var(--primary);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.85rem;
        }
        
        .create-template-form {
            margin-top: 24px;
        }
        
        .field-inputs {
            margin-top: 16px;
        }
        
        .field-input-row {
            display: flex;
            margin-bottom: 8px;
        }
        
        .field-input-row input {
            flex: 1;
            margin-right: 8px;
        }
        
        .field-input-row button {
            padding: 8px 12px;
            margin: 0;
        }
        
        .add-field-btn {
            background-color: var(--success);
            margin-top: 8px;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar">
        <div class="navbar-container">

            <a href="/" class="navbar-brand">
                <img src="/static/images/Formair-light.png" alt="Logo" class="navbar-logo">
                
            </a>
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a href="/" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="/template-manager" class="nav-link active">Template Manager</a>
                </li>
            </ul>
        </div>
    </nav>

    <div class="main-container">
        <div class="card">
            <h2>Template Manager</h2>
            <p>Create, edit, and manage your document extraction templates.</p>
            
            <div class="template-list">
                <h3>Your Templates</h3>
                
                <!-- Template items will be dynamically added here -->
                <div id="templateContainer"></div>
            </div>
            
            <div class="create-template-form">
                <h3>Create New Template</h3>
                <div class="form-group">
                    <label for="templateName">Template Name</label>
                    <input type="text" id="templateName" placeholder="e.g., Auto Insurance">
                </div>
                
                <div class="form-group">
                    <label for="templateDescription">Description (Optional)</label>
                    <input type="text" id="templateDescription" placeholder="e.g., For extracting auto insurance policy details">
                </div>
                
                <div class="form-group">
                    <label>Template Fields</label>
                    <div class="field-inputs" id="fieldInputs">
                        <div class="field-input-row">
                            <input type="text" placeholder="Field name (e.g., policy_number)" class="field-input">
                            <button class="delete-field-btn" onclick="removeField(this)">✕</button>
                        </div>
                    </div>
                    <button class="add-field-btn" onclick="addFieldInput()">+ Add Field</button>
                </div>
                
                <button id="saveTemplateBtn">Save Template</button>
            </div>
        </div>
    </div>

    <script>
        // Sample template data (in a real app, this would come from the server)
        const templates = [
            {
                id: 1,
                name: "Auto Insurance",
                description: "For extracting auto insurance policy details",
                fields: ["policy_number", "customer_name", "vehicle_make", "vehicle_model", "coverage_amount"]
            },
            {
                id: 2,
                name: "Home Insurance",
                description: "For extracting home insurance policy details",
                fields: ["policy_number", "customer_name", "property_address", "coverage_amount", "deductible"]
            },
            {
                id: 3,
                name: "Life Insurance",
                description: "For extracting life insurance policy details",
                fields: ["policy_number", "customer_name", "beneficiary", "coverage_amount", "term_length"]
            }
        ];
        
        // Render templates
        function renderTemplates() {
            const container = document.getElementById('templateContainer');
            container.innerHTML = '';
            
            templates.forEach(template => {
                const templateItem = document.createElement('div');
                templateItem.className = 'template-item';
                templateItem.dataset.id = template.id;
                
                const templateInfo = document.createElement('div');
                templateInfo.className = 'template-info';
                
                const title = document.createElement('h4');
                title.textContent = template.name;
                
                const description = document.createElement('p');
                description.textContent = template.description;
                
                const fieldList = document.createElement('div');
                fieldList.className = 'field-list';
                
                template.fields.forEach(field => {
                    const fieldItem = document.createElement('span');
                    fieldItem.className = 'field-item';
                    fieldItem.textContent = field;
                    fieldList.appendChild(fieldItem);
                });
                
                templateInfo.appendChild(title);
                templateInfo.appendChild(description);
                templateInfo.appendChild(fieldList);
                
                const actions = document.createElement('div');
                actions.className = 'template-actions';
                
                const editBtn = document.createElement('button');
                editBtn.className = 'edit-btn';
                editBtn.textContent = 'Edit';
                editBtn.onclick = () => editTemplate(template.id);
                
                const deleteBtn = document.createElement('button');
                deleteBtn.className = 'delete-btn';
                deleteBtn.textContent = 'Delete';
                deleteBtn.onclick = () => deleteTemplate(template.id);
                
                actions.appendChild(editBtn);
                actions.appendChild(deleteBtn);
                
                templateItem.appendChild(templateInfo);
                templateItem.appendChild(actions);
                
                container.appendChild(templateItem);
            });
        }
        
        // Add a new field input
        function addFieldInput() {
            const fieldInputs = document.getElementById('fieldInputs');
            const newRow = document.createElement('div');
            newRow.className = 'field-input-row';
            newRow.innerHTML = `
                <input type="text" placeholder="Field name (e.g., policy_number)" class="field-input">
                <button class="delete-field-btn" onclick="removeField(this)">✕</button>
            `;
            fieldInputs.appendChild(newRow);
        }
        
        // Remove a field input
        function removeField(button) {
            const row = button.parentElement;
            row.remove();
        }
        
        // Edit template
        function editTemplate(id) {
            const template = templates.find(t => t.id === id);
            if (!template) return;
            
            document.getElementById('templateName').value = template.name;
            document.getElementById('templateDescription').value = template.description;
            
            const fieldInputs = document.getElementById('fieldInputs');
            fieldInputs.innerHTML = '';
            
            template.fields.forEach(field => {
                const newRow = document.createElement('div');
                newRow.className = 'field-input-row';
                newRow.innerHTML = `
                    <input type="text" value="${field}" class="field-input">
                    <button class="delete-field-btn" onclick="removeField(this)">✕</button>
                `;
                fieldInputs.appendChild(newRow);
            });
            
            // Change save button to update
            const saveBtn = document.getElementById('saveTemplateBtn');
            saveBtn.textContent = 'Update Template';
            saveBtn.dataset.editId = id;
        }
        
        // Delete template
        function deleteTemplate(id) {
            if (confirm('Are you sure you want to delete this template?')) {
                // In a real app, you would send a request to the server
                const index = templates.findIndex(t => t.id === id);
                if (index !== -1) {
                    templates.splice(index, 1);
                    renderTemplates();
                }
            }
        }
        
        // Save or update template
        document.getElementById('saveTemplateBtn').addEventListener('click', function() {
            const name = document.getElementById('templateName').value.trim();
            const description = document.getElementById('templateDescription').value.trim();
            const fieldInputs = document.querySelectorAll('.field-input');
            
            if (!name) {
                alert('Please enter a template name');
                return;
            }
            
            const fields = [];
            fieldInputs.forEach(input => {
                const value = input.value.trim();
                if (value) {
                    fields.push(value);
                }
            });
            
            if (fields.length === 0) {
                alert('Please add at least one field');
                return;
            }
            
            // Check if we're editing or creating
            const editId = this.dataset.editId;
            
            if (editId) {
                // Update existing template
                const index = templates.findIndex(t => t.id === parseInt(editId));
                if (index !== -1) {
                    templates[index] = {
                        ...templates[index],
                        name,
                        description,
                        fields
                    };
                }
                
                // Reset edit mode
                this.textContent = 'Save Template';
                delete this.dataset.editId;
            } else {
                // Create new template
                const newId = templates.length > 0 ? Math.max(...templates.map(t => t.id)) + 1 : 1;
                templates.push({
                    id: newId,
                    name,
                    description,
                    fields
                });
            }
            
            // Reset form
            document.getElementById('templateName').value = '';
            document.getElementById('templateDescription').value = '';
            document.getElementById('fieldInputs').innerHTML = `
                <div class="field-input-row">
                    <input type="text" placeholder="Field name (e.g., policy_number)" class="field-input">
                    <button class="delete-field-btn" onclick="removeField(this)">✕</button>
                </div>
            `;
            
            // Refresh template list
            renderTemplates();
        });
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            renderTemplates();
        });
    </script>
</body>
</html>
