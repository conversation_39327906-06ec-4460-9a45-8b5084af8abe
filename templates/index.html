<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Insurance Form Extractor</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #4361ee;
            --primary-light: #4895ef;
            --secondary: #3f37c9;
            --success: #4cc9f0;
            --danger: #f72585;
            --light: #f8f9fa;
            --dark: #212529;
            --gray: #6c757d;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: var(--dark);
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Roboto', sans-serif;
            font-weight: 500;
        }

        button, input, select, textarea {
            font-family: 'Roboto', sans-serif;
        }

        .navbar-brand {
            font-family: 'Roboto', sans-serif;
            font-weight: 700;
        }

        .nav-link {
            font-family: 'Roboto', sans-serif;
            font-weight: 500;
        }

        .file-name {
            font-family: 'Roboto', sans-serif;
            font-weight: 400;
        }

        .file-status {
            font-family: 'Roboto', sans-serif;
            font-weight: 400;
        }

        .extracted-text {
            font-family: 'Roboto Mono', 'Roboto', monospace;
        }

        /* Navbar styles */
        .navbar {
            background-color: var(--primary);
            color: white;
            padding: 0 24px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .navbar-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1600px;
            margin: 0 auto;
            height: 64px;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.5rem;
            font-weight: 600;
            text-decoration: none;
            color: white;
        }

        .navbar-logo {
            height: 40px;
            width: auto;
            margin: 12px 0;
        }

        .navbar-nav {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .nav-item {
            margin-left: 24px;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.85);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 0;
            transition: var(--transition);
            position: relative;
        }

        .nav-link:hover {
            color: white;
        }

        .nav-link.active {
            color: white;
        }

        .nav-link.active:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: white;
            border-radius: 3px 3px 0 0;
        }

        .main-container {
            display: flex;
            min-height: calc(100vh - 64px);
            gap: 24px;
            padding: 24px;
            max-width: 1600px;
            margin: 0 auto;
            align-items: flex-start;
        }

        .column {
            flex: 1;
            padding: 24px;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            position: relative;
            overflow-y: auto;
            max-height: 95vh;
        }

        h2 {
            color: var(--primary);
            font-weight: 600;
            margin-top: 0;
            padding-bottom: 12px;
            border-bottom: 1px solid #eee;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--dark);
        }

        input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 1px dashed var(--primary-light);
            border-radius: var(--border-radius);
            background-color: rgba(67, 97, 238, 0.05);
            transition: var(--transition);
        }

        input[type="file"]:hover {
            background-color: rgba(67, 97, 238, 0.1);
        }

        button {
            background-color: var(--primary);
            color: white;
            padding: 12px 18px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 500;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            margin-bottom: 12px;
        }

        button:hover {
            background-color: var(--secondary);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        .pdf-viewer {
            width: 100%;
            height: 85vh;
            border-radius: var(--border-radius);
            border: 1px solid #eee;
            margin-top: 20px;
            box-shadow: var(--box-shadow);
            background-color: #fff;
        }

        .pdf-viewer-container {
            width: 100%;
            height: calc(85vh + 40px);
            position: relative;
            overflow: hidden;
            border-radius: var(--border-radius);
            background-color: #f5f5f5;
            margin-bottom: 20px;
        }

        #pdfViewer {
            width: 100%;
            height: 100%;
            border: none;
            display: block;
        }

        .template-field {
            margin-bottom: 20px;
        }

        .template-field input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .template-field input:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
            outline: none;
        }

        #loadingIndicator {
            margin-top: 12px;
            color: var(--gray);
            font-style: italic;
            display: flex;
            align-items: center;
        }

        #loadingIndicator:before {
            content: "";
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid var(--primary-light);
            border-radius: 50%;
            border-top-color: transparent;
            margin-right: 8px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .switch-container {
            display: flex;
            align-items: center;
            margin: 20px 0;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 200px;
            height: 36px;
            margin: 0 10px;
            z-index: 1;
        }

        .switch input {
            opacity: 0;
            width: 100%;
            height: 100%;
            position: absolute;
            z-index: 3;
            cursor: pointer;
            margin: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #eee;
            transition: .4s;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 10px;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
            pointer-events: none;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 28px;
            width: 100px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: calc(var(--border-radius) - 2px);
            z-index: 1;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .slider-text {
            color: var(--gray);
            font-size: 13px;
            font-weight: 500;
            z-index: 2;
            position: relative;
            width: 100px;
            text-align: center;
            transition: var(--transition);
        }

        /* Add hover effects */
        .switch:hover .slider {
            background-color: #e0e0e0;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.2);
        }

        .switch:hover .slider:before {
            box-shadow: 0 2px 4px rgba(0,0,0,0.15);
        }

        .switch:hover .slider-text {
            color: var(--dark);
        }

        input:checked + .slider {
            background-color: var(--primary-light);
        }

        input:checked + .slider:before {
            transform: translateX(92px);
        }

        input:checked + .slider .pdf-text {
            color: #999;
        }

        input:checked + .slider .text-text {
            color: #000;
        }

        input:not(:checked) + .slider .pdf-text {
            color: #000;
        }

        input:not(:checked) + .slider .text-text {
            color: #999;
        }

        /* Add focus styles for accessibility */
        .switch input:focus + .slider {
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
        }

        /* Disabled state */
        .switch input:disabled + .slider {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .switch input:disabled + .slider:before {
            background-color: #f5f5f5;
        }

        .extracted-text {
            white-space: pre-wrap;
            border: 1px solid #eee;
            padding: 20px;
            height: 70vh;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            background-color: #fafafa;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            line-height: 1.5;
        }

        input[type="checkbox"]:disabled + .slider {
            opacity: 0.6;
            cursor: not-allowed;
        }

        #generateAnswersBtn {
            background-color: var(--success);
            color: white;
            padding: 14px 24px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 600;
            margin-top: 15px;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
        }

        #generateAnswersBtn:hover {
            background-color: var(--primary);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }

        .action-btn {
            background-color: var(--primary);
            color: white;
            padding: 10px 14px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            margin: 5px;
            transition: var(--transition);
        }

        .action-btn:hover {
            background-color: var(--secondary);
            transform: translateY(-2px);
        }

        .bounding-box {
            position: absolute;
            border: 2px solid var(--danger);
            background-color: rgba(247, 37, 133, 0.1);
            pointer-events: none;
            border-radius: 2px;
        }

        /* Template selection styles */
        .template-selector {
            margin-bottom: 24px;
        }

        .template-card {
            border: 1px solid #eee;
            border-radius: var(--border-radius);
            padding: 16px;
            margin-bottom: 16px;
            cursor: pointer;
            transition: var(--transition);
        }

        .template-card:hover {
            border-color: var(--primary-light);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .template-card.selected {
            border-color: var(--primary);
            background-color: rgba(67, 97, 238, 0.05);
        }

        .template-card h4 {
            margin: 0 0 8px 0;
            color: var(--dark);
        }

        .template-card p {
            margin: 0;
            color: var(--gray);
            font-size: 0.9rem;
        }

        .document-list-container {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 16px;
            max-height: 200px;
            overflow-y: auto;
        }

        .document-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border: 1px solid #eee;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
        }

        .document-item:hover {
            background-color: rgba(67, 97, 238, 0.05);
        }

        .document-item.active {
            background-color: rgba(67, 97, 238, 0.1);
            border-color: var(--primary);
        }

        .view-toggle-container {
            display: flex;
            justify-content: center;
            margin-bottom: 16px;
        }

        .document-navigation {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        #currentDocTitle {
            font-weight: 500;
            color: var(--dark);
        }

        .pdf-viewer-container {
            margin-bottom: 16px;
        }

        .extracted-text-view {
            display: none;
            white-space: pre-wrap;
            border: 1px solid #eee;
            padding: 20px;
            height: 70vh;
            width: 100%;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            background-color: #fafafa;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            line-height: 1.5;
            resize: none; /* Prevent textarea resizing */
            outline: none; /* Remove default focus outline */
        }

        .extracted-text-view:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
        }

        .collapsible-selector {
            margin-bottom: 20px;
            border: 1px solid #eee;
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .selector-header {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            background-color: #f8f9fa;
            cursor: pointer;
            transition: var(--transition);
            user-select: none;
        }

        .selector-header:hover {
            background-color: #f0f2f5;
        }

        .selector-header i:last-child {
            margin-left: auto;
            transition: transform 0.3s ease;
        }

        .selector-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
            background-color: white;
        }

        .selector-content.expanded {
            max-height: 300px;
            border-top: 1px solid #eee;
        }

        .view-slider-container {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
        }

        .view-slider {
            flex: 1;
            position: relative;
            height: 36px;
        }

        .view-slider input[type="range"] {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
            z-index: 2;
        }

        .slider-track {
            position: absolute;
            width: 100%;
            height: 4px;
            background-color: #eee;
            border-radius: 2px;
            top: 50%;
            transform: translateY(-50%);
        }

        .slider-thumb {
            position: absolute;
            width: 20px;
            height: 20px;
            background-color: var(--primary);
            border-radius: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            transition: left 0.3s ease;
        }

        .view-label {
            font-weight: 500;
            color: var(--gray);
            min-width: 80px;
        }

        .pdf-viewer-container,
        .extracted-text-view {
            transition: opacity 0.3s ease;
        }

        .pdf-viewer-container.hidden,
        .extracted-text-view.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .document-preview-section {
            display: none;
            margin-top: 24px;
        }

        .upload-section {
            position: relative;
            padding: 20px;
            border: 2px dashed var(--primary-light);
            border-radius: var(--border-radius);
            background-color: rgba(67, 97, 238, 0.05);
            transition: var(--transition);
            text-align: center;
            margin-bottom: 24px;
            min-height: 150px;
        }

        .upload-section.drag-over {
            background-color: rgba(67, 97, 238, 0.1);
            border-color: var(--primary);
        }

        .upload-section .upload-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100px;
        }

        .upload-section .upload-content.hidden {
            display: none;
        }

        .upload-section .file-list {
            display: none;
            width: 100%;
            text-align: left;
            margin-top: 15px;
        }

        .upload-section .file-list.show {
            display: block;
        }

        .upload-section .file-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            background: white;
            border-radius: var(--border-radius);
            margin-bottom: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .upload-section .file-item i {
            color: var(--primary);
        }

        .upload-section .file-item .file-name {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .upload-section .file-item .file-status {
            font-size: 0.8em;
            color: var(--gray);
        }

        .upload-section .file-item .file-status.extracted {
            color: var(--success);
        }

        .upload-section .file-item .file-status.processing {
            color: var(--primary);
        }

        .upload-section .drag-text {
            color: var(--gray);
            margin: 10px 0;
            font-size: 0.9rem;
        }

        /* Modern navigation arrows */
        .navigation-controls {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .nav-arrow {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: white;
            border: 1px solid #eee;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: var(--primary);
        }

        .nav-arrow:not(:disabled):hover {
            background: var(--primary);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .nav-arrow:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            background: #f5f5f5;
            color: #ccc;
            border-color: #eee;
            pointer-events: none;
        }

        .nav-arrow i {
            font-size: 1.2em;
        }

        /* Extract button styles */
        #extractBtnContainer {
            text-align: center;
            margin: 20px 0;
            position: relative;
            z-index: 0;
            transform: none !important;
        }

        #extractBtn {
            background-color: var(--success);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-top: 15px;
            position: relative;
            transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
            isolation: isolate;
        }

        #extractBtn:hover {
            background-color: var(--primary);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }

        #extractBtn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        #extractBtn i {
            font-size: 1.1em;
        }

        /* Ensure view toggle container stays in place */
        .view-toggle-container {
            margin: 0;
            position: relative;
            z-index: 1;
            transform: none !important;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 200px;
            height: 36px;
            margin: 0 10px;
            z-index: 1;
        }

        /* View toggle disabled state */
        .view-toggle-container.disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

        .view-toggle-container.disabled::after {
            content: 'Extraction not started';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.8em;
            color: var(--gray);
            white-space: nowrap;
        }

        .view-toggle-container.disabled .switch {
            pointer-events: none;
        }

        /* Unified document navigation bar */
        .document-navigation-bar {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 12px 16px;
            background-color: #f8f9fa;
            border: 1px solid #eee;
            border-radius: var(--border-radius);
            margin-bottom: 16px;
            position: relative;
            z-index: 1000;
            flex-wrap: wrap;
            min-height: 60px;
        }

        .document-selector {
            flex: 1;
            min-width: 200px;
            max-width: 300px;
            position: relative;
            z-index: 1001;
        }

        .document-selector-trigger {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border: 1px solid #eee;
            border-radius: var(--border-radius);
            cursor: pointer;
            background-color: white;
            width: 100%;
            white-space: nowrap;
            overflow: hidden;
        }

        .document-selector-content {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background-color: white;
            border: 1px solid #eee;
            border-radius: var(--border-radius);
            margin-top: 4px;
            max-height: 200px;
            overflow-y: auto;
            display: none;
            z-index: 1002;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .document-selector-content.show {
            display: block;
        }

        .view-toggle-container {
            margin: 0;
            position: relative;
            z-index: 1001;
            flex-shrink: 0;
        }

        .save-button-container {
            margin: 0 10px;
            position: relative;
            z-index: 1001;
            flex-shrink: 0;
        }

        #saveTextBtn {
            background-color: var(--success);
            padding: 8px 12px;
            transition: all 0.3s ease;
        }

        #saveTextBtn:hover:not(:disabled) {
            background-color: var(--primary);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        #saveTextBtn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            background-color: #ccc;
        }

        .navigation-controls {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-left: auto;
            flex-shrink: 0;
            position: relative;
            z-index: 1001;
        }

        .nav-arrow {
            width: 36px;
            height: 36px;
            min-width: 36px;
            border-radius: 50%;
            background: white;
            border: 1px solid #eee;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: var(--primary);
            padding: 0;
        }

        #currentDocTitle {
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 120px;
            display: block;
        }

        @media (max-width: 768px) {
            .document-navigation-bar {
                gap: 8px;
                padding: 8px;
            }

            .document-selector {
                max-width: none;
                flex: 1;
            }

            .navigation-controls {
                margin-left: 0;
                width: 100%;
                justify-content: center;
            }

            .view-toggle-container {
                width: 100%;
                display: flex;
                justify-content: center;
            }
        }

        .alert-container {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 2000;
            display: flex;
            flex-direction: column;
            gap: 10px;
            pointer-events: none;
        }

        .alert {
            padding: 12px 20px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 12px;
            min-width: 300px;
            max-width: 500px;
            pointer-events: auto;
            animation: slideIn 0.3s ease-out;
        }

        .alert-error {
            background-color: #ffebee;
            color: #c62828;
        }

        .alert-success {
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        .alert-warning {
            background-color: #fff8e1;
            color: #f57f17;
        }

        .alert-info {
            background-color: #e3f2fd;
            color: #1565c0;
        }

        .alert .close-btn {
            margin-left: auto;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.2s;
        }

        .alert .close-btn:hover {
            opacity: 1;
        }

        @keyframes slideIn {
            from {
                transform: translateY(-100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateY(0);
                opacity: 1;
            }
            to {
                transform: translateY(-100%);
                opacity: 0;
            }
        }

        .document-selector-trigger {
            max-width: 300px;
            overflow: hidden;
        }

        .document-selector-trigger span {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: block;
        }

        #currentDocumentName {
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 250px;
            display: block;
        }

        .document-preview-section {
            position: relative;
            z-index: 1;
            transform: none !important;
        }

        .viewer-section {
            position: relative;
            z-index: 1;
            transform: none !important;
        }

        #pdfViewerContainer {
            position: relative;
            z-index: 1;
            transform: none !important;
        }

        .pdf-viewer {
            position: relative;
            z-index: 1;
            transform: none !important;
        }

        /* Add styles for the add more files button */
        .add-more-files {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px;
            margin-top: 12px;
            border-top: 1px solid #eee;
            cursor: pointer;
            color: var(--primary);
            transition: var(--transition);
        }

        .add-more-files:hover {
            background-color: rgba(67, 97, 238, 0.05);
        }

        .add-more-files i {
            margin-right: 8px;
            font-size: 1.2em;
        }

        .upload-section .file-item {
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .upload-section .file-item:hover {
            background-color: rgba(67, 97, 238, 0.05);
        }

        .upload-section.drag-over .upload-content,
        .upload-section.drag-over .file-list {
            display: none !important;
        }

        .upload-section.drag-over::after {
            content: "Drop files here";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.5em;
            color: var(--primary);
            font-weight: 500;
        }

        /* Add tab system styles */
        .tab-system {
            margin-bottom: 24px;
        }

        .tab-buttons {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
        }

        .tab-button {
            padding: 8px 16px;
            border: none;
            background: none;
            cursor: pointer;
            font-weight: 500;
            color: var(--gray);
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
        }

        .tab-button.active {
            color: var(--primary);
            background-color: rgba(67, 97, 238, 0.1);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .recent-documents {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .recent-document-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border: 1px solid #eee;
            border-radius: var(--border-radius);
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .recent-document-item:hover {
            background-color: rgba(67, 97, 238, 0.05);
            border-color: var(--primary-light);
        }

        .recent-document-item .file-info {
            flex: 1;
            min-width: 0;
        }

        .recent-document-item .file-name {
            font-weight: 500;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .recent-document-item .file-meta {
            font-size: 0.85em;
            color: var(--gray);
        }

        .recent-document-item .file-status {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 0.85em;
        }

        .recent-document-item .file-status.extracted {
            color: var(--success);
        }

        .recent-document-item .file-status.not-extracted {
            color: var(--gray);
        }

        .recent-document-item .file-status.error {
            color: var(--danger);
        }

        .recent-document-item .add-btn {
            padding: 6px 12px;
            border: 1px solid var(--primary);
            border-radius: var(--border-radius);
            background: none;
            color: var(--primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .recent-document-item .add-btn:hover {
            background-color: var(--primary);
            color: white;
        }

        .recent-document-item .add-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .recent-document-item .file-status.processed {
            color: var(--success);
        }

        .recent-document-item .file-status.not-processed {
            color: var(--danger);
        }

        .recent-document-item .add-btn.added {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }

        .recent-document-item .remove-btn {
            padding: 6px;
            border: none;
            background: none;
            color: var(--danger);
            cursor: pointer;
            transition: all 0.3s ease;
            margin-left: 8px;
        }

        .recent-document-item .remove-btn:hover {
            transform: scale(1.2);
        }

        /* Add CSS for the processing status */
        .recent-document-item .file-status.processing {
            color: var(--primary);
        }

        .recent-document-item .file-status.error {
            color: var(--danger);
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar">
        <div class="navbar-container">
            <a href="/" class="navbar-brand">
                <img src="/static/images/Formair-light.png" alt="Logo" class="navbar-logo">

            </a>
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a href="/" class="nav-link active">Home</a>
                </li>
                <li class="nav-item">
                    <a href="/template-manager" class="nav-link">Template Manager</a>
                </li>
            </ul>
        </div>
    </nav>

    <div class="main-container">
        <!-- Left Column - PDF Upload and Viewer -->
        <div class="column" id="pdfColumn">
            <h2>Document Management</h2>

            <div class="tab-system">
                <div class="tab-buttons">
                    <button class="tab-button active" data-tab="upload">Upload Documents</button>
                    <button class="tab-button" data-tab="recent">Recently Used</button>
                </div>

                <div class="tab-content active" id="uploadTab">
                    <div class="upload-section" id="uploadSection">
                        <div class="upload-content">
                            <form id="uploadForm" enctype="multipart/form-data">
                                <input type="file" id="pdfFiles" name="files[]" accept="application/pdf" multiple required style="display: none;">
                                <i class="fas fa-cloud-upload-alt" style="font-size: 2em; color: var(--primary);"></i>
                                <h3>Upload PDF Documents</h3>
                                <p class="drag-text">Drag and drop up to 10 PDF files here or click to browse</p>
                            </form>
                        </div>
                        <div class="file-list" id="fileList">
                            <!-- File items will be added here dynamically -->
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="recentTab">
                    <div class="recent-documents" id="recentDocumentsList">
                        <!-- Recent documents will be added here dynamically -->
                    </div>
                </div>
            </div>

            <div id="loadingIndicator" style="display: none;">
                Processing files... Please wait.
            </div>

            <div id="extractBtnContainer" style="text-align: center; margin: 20px 0;">
                <button id="extractBtn" disabled>
                    <i class="fas fa-magic"></i>
                    Extract Text
                </button>
            </div>

            <div class="document-preview-section">
                <h2>Document Preview</h2>
                <div class="document-navigation-bar" style="display: none;">
                    <div class="document-selector">
                        <div class="document-selector-trigger">
                            <i class="fas fa-file-pdf"></i>
                            <span id="currentDocumentName">Select a document</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="document-selector-content">
                            <div id="documentList" class="document-list-container"></div>
                        </div>
                    </div>

                    <div class="view-toggle-container">
                        <label class="switch">
                            <input type="checkbox" id="viewToggle">
                            <span class="slider">
                                <span class="slider-text pdf-text">PDF View</span>
                                <span class="slider-text text-text">Text View</span>
                            </span>
                        </label>
                    </div>

                    <div class="save-button-container">
                        <button id="saveTextBtn" class="action-btn" disabled>
                            <i class="fas fa-save"></i> Save Text
                        </button>
                    </div>

                    <div class="navigation-controls">
                        <span id="currentDocTitle">Document 1 of 1</span>
                        <button id="prevDoc" class="nav-arrow">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button id="nextDoc" class="nav-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>

                <div class="viewer-section">
                    <div id="pdfViewerContainer" class="pdf-viewer-container">
                        <iframe id="pdfViewer" class="pdf-viewer"></iframe>
                    </div>

                    <textarea id="extractedTextView" class="extracted-text-view" spellcheck="false"></textarea>
                </div>
            </div>

            <div id="generateBtnContainer" style="text-align: center; margin-top: 20px;">
                <button id="generateAnswersBtn" style="display: none;">Generate Answers</button>
            </div>
        </div>

        <!-- Right Column - Template Selection and Fields -->
        <div class="column" id="formColumn">
            <h2>Form Template</h2>

            <div class="collapsible-selector">
                <div class="selector-header" id="templateSelectorHeader">
                    <i class="fas fa-file-alt"></i>
                    <span id="currentTemplateName">Select a template</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="selector-content" id="templateSelectorContent">
                    <div class="template-card" data-template="auto_insurance">
                        <h4>Auto Insurance</h4>
                        <p>Template for auto insurance documents</p>
                    </div>
                    <div class="template-card" data-template="home_insurance">
                        <h4>Home Insurance</h4>
                        <p>Template for home insurance documents</p>
                    </div>
                    <div class="template-card" data-template="life_insurance">
                        <h4>Life Insurance</h4>
                        <p>Template for life insurance documents</p>
                    </div>
                </div>
            </div>

            <div id="templateFields" style="display: none;">
                <h3>Template Fields</h3>
                <div id="fieldsContainer"></div>
                <button id="exportBtn">Export as CSV</button>
            </div>
        </div>
    </div>

    <!-- Add alert container to body -->
    <div class="alert-container" id="alertContainer"></div>

    <script>
        // Global variables
        let extractedTexts = {}; // Dictionary to store extracted text for each file
        let templateLoaded = false;
        let currentTemplate = null;
        let extractedData = null;
        let uploadedFiles = [];
        let currentFileIndex = -1;
        let fileStatuses = {}; // Track extraction status for each file

        // Add extraction state management
        const EXTRACTION_STATES = {
            NOT_STARTED: 'not_started',
            IN_PROGRESS: 'in_progress',
            COMPLETED: 'completed',
            ERROR: 'error'
        };

        // Load existing files when the page loads
        document.addEventListener('DOMContentLoaded', async function() {
            // Disable the view toggle initially
            document.getElementById('viewToggle').disabled = true;

            // Initialize text colors
            const pdfText = document.querySelector('.pdf-text');
            const textText = document.querySelector('.text-text');
            if (pdfText && textText) {
                pdfText.style.color = '#000';
                textText.style.color = '#999';
            }

            // Add event listeners to template cards
            const templateCards = document.querySelectorAll('.template-card');
            templateCards.forEach(card => {
                card.addEventListener('click', function() {
                    // Remove selected class from all cards
                    templateCards.forEach(c => c.classList.remove('selected'));

                    // Add selected class to clicked card
                    this.classList.add('selected');

                    // Load the template
                    const templateType = this.dataset.template;
                    loadPredefinedTemplate(templateType);

                    // Set template loaded flag
                    templateLoaded = true;

                    // Update Generate Answers button visibility
                    updateGenerateAnswersButton();

                    // Collapse the template selector
                    const templateContent = document.getElementById('templateSelectorContent');
                    const templateChevron = document.getElementById('templateSelectorHeader').querySelector('.fa-chevron-down');
                    templateContent.classList.remove('expanded');
                    templateChevron.style.transform = '';
                });
            });

            // Hide document preview section initially
            const documentPreviewSection = document.querySelector('.document-preview-section');
            const navigationBar = document.querySelector('.document-navigation-bar');
            if (documentPreviewSection) {
                documentPreviewSection.style.display = 'none';
            }
            if (navigationBar) {
                navigationBar.style.display = 'none';
            }
        });

        // File selection handler
        document.getElementById('pdfFiles').addEventListener('change', function(e) {
            if (this.files.length > 10) {
                alert('Please select no more than 10 files');
                this.value = '';
                return;
            }

            // Submit the form automatically after files are selected
            document.getElementById('uploadForm').dispatchEvent(new Event('submit'));
        });

        // File Upload Form
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            document.getElementById('loadingIndicator').style.display = 'block';

            const fileInput = document.getElementById('pdfFiles');
            const files = fileInput.files;

            if (!files.length) {
                alert('Please select at least one PDF file');
                document.getElementById('loadingIndicator').style.display = 'none';
                return;
            }

            const formData = new FormData();
            Array.from(files).forEach(file => {
                formData.append('files[]', file);
            });

            try {
                const response = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                console.log('Upload response:', data);

                if (response.ok) {
                    // Store the uploaded files data
                    const newFiles = data.files.map(file => {
                        return {
                            name: file.name,
                            url: file.url.startsWith('s3://') ? `/serve_file/${file.name}` : file.url,
                            upload_time: file.upload_time,
                            extracted_text: file.extracted_text || ''
                        };
                    });

                    // Append new files to the existing files instead of replacing them
                    uploadedFiles = [...uploadedFiles, ...newFiles];

                    // Update file list display
                    updateFileList();

                    // Show the document preview section
                    const documentPreviewSection = document.querySelector('.document-preview-section');
                    const navigationBar = document.querySelector('.document-navigation-bar');
                    if (documentPreviewSection) {
                        documentPreviewSection.style.display = 'block';
                    }
                    if (navigationBar) {
                        navigationBar.style.display = 'flex';
                    }

                    // Update the document list
                    updateDocumentList();

                    // Show the last uploaded document
                    if (uploadedFiles.length > 0) {
                        currentFileIndex = uploadedFiles.length - 1;
                        showDocument(currentFileIndex);
                    }

                    // Enable extract button if not already enabled
                    document.getElementById('extractBtn').disabled = false;

                    // Clear the file input
                    fileInput.value = '';
                } else {
                    console.error('Upload error:', data);
                    alert('Error: ' + (data.error || 'Unknown error occurred'));
                }
            } catch (error) {
                console.error('Upload exception:', error);
                alert('An error occurred while uploading the files');
            }

            document.getElementById('loadingIndicator').style.display = 'none';
        });

        // Export Button
        document.getElementById('exportBtn').addEventListener('click', async function() {
            if (!extractedData || extractedData.length === 0) {
                alert('No data to export');
                return;
            }

            try {
                const response = await fetch('/export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        template_type: currentTemplate,
                        extracted_data: extractedData
                    })
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'extracted_data.csv';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    a.remove();
                } else {
                    const data = await response.json();
                    alert('Error: ' + data.error);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('An error occurred while exporting the data');
            }
        });

        // Generate Answers Button
        document.getElementById('generateAnswersBtn').addEventListener('click', async function() {
            const currentFile = uploadedFiles[currentFileIndex];
            if (!currentFile || !extractedTexts[currentFile.name]) {
                alert('Please process a document first');
                return;
            }

            document.getElementById('loadingIndicator').style.display = 'block';

            try {
                // Get the current template fields and their values
                const fieldElements = document.querySelectorAll('#fieldsContainer input');
                const fields = Array.from(fieldElements).map(input => input.name);
                const formAnswers = {};
                fieldElements.forEach(input => {
                    formAnswers[input.name] = input.value;
                });

                // Get the current template type from the selected card
                const selectedCard = document.querySelector('.template-card.selected');
                const formType = selectedCard ? selectedCard.dataset.template : 'auto_insurance';

                // Call the API to generate answers
                const response = await fetch('/generate_answers', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        extracted_text: extractedTexts[currentFile.name],
                        fields: fields,
                        form_type: formType,
                        ...formAnswers  // Include all current form answers
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    extractedData = data.extracted_data;
                    populateFormFields(extractedData[0]);

                    // Log the prompt for debugging
                    console.log('Generated Prompt:', data.prompt);
                } else {
                    alert('Error: ' + data.error);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('An error occurred while generating answers');
            }

            document.getElementById('loadingIndicator').style.display = 'none';
        });

        // Helper function to update the Generate Answers button visibility
        function updateGenerateAnswersButton() {
            const generateBtn = document.getElementById('generateAnswersBtn');
            const currentFile = uploadedFiles[currentFileIndex];

            if (currentFile && extractedTexts[currentFile.name] && templateLoaded) {
                generateBtn.style.display = 'block';
                generateBtn.style.position = 'fixed';
                generateBtn.style.bottom = '30px';
                generateBtn.style.left = '45%'; // Center in the left column
                generateBtn.style.transform = 'translateX(-50%)';
                generateBtn.style.zIndex = '100';
            } else {
                generateBtn.style.display = 'none';
            }
        }

        // Helper Functions
        function showPdfViewer(file) {
            const pdfViewer = document.getElementById('pdfViewer');
            const pdfUrl = URL.createObjectURL(file);
            pdfViewer.src = pdfUrl;
            document.getElementById('pdfViewerContainer').style.display = 'block';

            // Reset the toggle to PDF view
            document.getElementById('viewToggle').checked = false;
            document.getElementById('pdfViewer').style.display = 'block';
            document.getElementById('extractedTextView').style.display = 'none';
        }

        function loadPredefinedTemplate(templateType) {
            currentTemplate = templateType;
            const currentTemplateName = document.getElementById('currentTemplateName');

            const templateNames = {
                "auto_insurance": "Auto Insurance",
                "home_insurance": "Home Insurance",
                "life_insurance": "Life Insurance"
            };

            currentTemplateName.textContent = templateNames[templateType];

            const templates = {
                "auto_insurance": ["policy_number", "customer_name", "vehicle_make", "vehicle_model", "coverage_amount"],
                "home_insurance": ["policy_number", "customer_name", "property_address", "coverage_amount", "deductible"],
                "life_insurance": ["policy_number", "customer_name", "beneficiary", "coverage_amount", "term_length"]
            };

            createFormFields(templates[templateType]);
            document.getElementById('templateFields').style.display = 'block';
        }

        function parseTemplateCSV(csvContent) {
            const lines = csvContent.split('\n');
            if (lines.length > 0) {
                const fields = lines[0].split(',').map(field => field.trim());
                createFormFields(fields);
            }
        }

        function createFormFields(fields) {
            const container = document.getElementById('fieldsContainer');
            container.innerHTML = '';

            fields.forEach(field => {
                const fieldDiv = document.createElement('div');
                fieldDiv.className = 'template-field';

                const label = document.createElement('label');
                label.textContent = field.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());

                const input = document.createElement('input');
                input.type = 'text';
                input.name = field;

                fieldDiv.appendChild(label);
                fieldDiv.appendChild(input);
                container.appendChild(fieldDiv);
            });

            document.getElementById('templateFields').style.display = 'block';
        }

        function populateFormFields(data) {
            const container = document.getElementById('fieldsContainer');
            container.innerHTML = '';

            // Get the current template type from the selected card
            const selectedCard = document.querySelector('.template-card.selected');
            const templateType = selectedCard ? selectedCard.dataset.template : 'auto_insurance';

            // Get the ordered fields for this template type
            const templates = {
                "auto_insurance": ["policy_number", "customer_name", "vehicle_make", "vehicle_model", "coverage_amount"],
                "home_insurance": ["policy_number", "customer_name", "property_address", "coverage_amount", "deductible"],
                "life_insurance": ["policy_number", "customer_name", "beneficiary", "coverage_amount", "term_length"]
            };

            const orderedFields = templates[templateType];

            // Create fields in the correct order
            orderedFields.forEach(field => {
                const fieldDiv = document.createElement('div');
                fieldDiv.className = 'template-field';

                const label = document.createElement('label');
                label.textContent = field.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());

                const input = document.createElement('input');
                input.type = 'text';
                input.name = field;
                input.value = data[field] || '';

                fieldDiv.appendChild(label);
                fieldDiv.appendChild(input);
                container.appendChild(fieldDiv);
            });

            document.getElementById('templateFields').style.display = 'block';
        }

        // Update the view toggle event listener
        document.getElementById('viewToggle').addEventListener('change', function() {
            const currentFile = uploadedFiles[currentFileIndex];
            if (!currentFile || !extractedTexts[currentFile.name]) {
                this.checked = false;
                showAlert('Please process the document before viewing extracted text');
                return;
            }

            const pdfViewerContainer = document.getElementById('pdfViewerContainer');
            const extractedTextView = document.getElementById('extractedTextView');
            const pdfText = document.querySelector('.pdf-text');
            const textText = document.querySelector('.text-text');

            const saveTextBtn = document.getElementById('saveTextBtn');

            if (this.checked) {
                extractedTextView.style.display = 'block';
                pdfViewerContainer.style.display = 'none';
                extractedTextView.value = extractedTexts[currentFile.name];
                pdfText.style.color = '#999';
                textText.style.color = '#000';
                saveTextBtn.disabled = false; // Enable save button in text view
            } else {
                pdfViewerContainer.style.display = 'block';
                extractedTextView.style.display = 'none';
                pdfText.style.color = '#000';
                textText.style.color = '#999';
                saveTextBtn.disabled = true; // Disable save button in PDF view
            }
        });

        // Initialize text colors when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            const pdfText = document.querySelector('.pdf-text');
            const textText = document.querySelector('.text-text');
            if (pdfText && textText) {
                pdfText.style.color = '#000';
                textText.style.color = '#999';
            }
        });

        // Document navigation
        document.getElementById('prevDoc').addEventListener('click', () => {
            if (currentFileIndex > 0) {
                currentFileIndex--;
                showDocument(currentFileIndex);
            }
        });

        document.getElementById('nextDoc').addEventListener('click', () => {
            if (currentFileIndex < uploadedFiles.length - 1) {
                currentFileIndex++;
                showDocument(currentFileIndex);
            }
        });

        function updateDocumentList() {
            const documentList = document.getElementById('documentList');
            documentList.innerHTML = '';

            uploadedFiles.forEach((file, index) => {
                const docItem = document.createElement('div');
                docItem.className = `document-item ${index === currentFileIndex ? 'active' : ''}`;
                docItem.innerHTML = `
                    <i class="fas fa-file-pdf"></i>
                    <span>${file.name}</span>
                `;
                docItem.addEventListener('click', () => {
                    currentFileIndex = index;
                    showDocument(currentFileIndex);
                    // Collapse the document selector
                    const documentSelectorContent = document.querySelector('.document-selector-content');
                    if (documentSelectorContent) {
                        documentSelectorContent.classList.remove('show');
                    }
                });
                documentList.appendChild(docItem);
            });
        }

        function showDocument(index) {
            if (index < 0 || index >= uploadedFiles.length) return;

            const file = uploadedFiles[index];
            console.log('Showing document:', file);

            // Update navigation bar visibility
            const navigationBar = document.querySelector('.document-navigation-bar');
            if (navigationBar) {
                navigationBar.style.display = 'flex';
            }

            // Update current document name with auto-scaling
            const currentDocumentName = document.getElementById('currentDocumentName');
            if (currentDocumentName) {
                currentDocumentName.textContent = file.name;

                // Auto-scale font size based on length
                if (file.name.length > 30) {
                    currentDocumentName.style.fontSize = '12px';
                } else {
                    currentDocumentName.style.fontSize = '14px';
                }
            }

            // Update document count
            const currentDocTitle = document.getElementById('currentDocTitle');
            if (currentDocTitle) {
                currentDocTitle.textContent = `Document ${index + 1} of ${uploadedFiles.length}`;
            }

            // Update PDF viewer
            const pdfViewer = document.getElementById('pdfViewer');
            const pdfViewerContainer = document.getElementById('pdfViewerContainer');
            const extractedTextView = document.getElementById('extractedTextView');

            if (pdfViewer && pdfViewerContainer) {
                let viewerUrl = file.url;
                if (viewerUrl.startsWith('s3://')) {
                    const parts = viewerUrl.replace('s3://', '').split('/');
                    viewerUrl = `/serve_file/${parts[parts.length - 1]}`;
                }

                console.log('Setting PDF viewer URL:', viewerUrl);
                pdfViewer.src = viewerUrl;
                pdfViewerContainer.style.display = 'block';
            }

            // Update extracted text if available
            if (extractedTextView && extractedTexts[file.name]) {
                extractedTextView.value = extractedTexts[file.name];
            }

            // Update view toggle state
            updateViewToggleState();

            // Update document list active state
            updateDocumentList();

            // Update navigation
            updateDocumentNavigation();

            // Update Generate Answers button visibility
            updateGenerateAnswersButton();
        }

        // Update the document preview section visibility
        function updateDocumentPreviewVisibility(show) {
            const documentPreviewSection = document.querySelector('.document-preview-section');
            const navigationBar = document.querySelector('.document-navigation-bar');

            if (documentPreviewSection) {
                documentPreviewSection.style.display = show ? 'block' : 'none';
            }

            if (navigationBar) {
                navigationBar.style.display = show ? 'flex' : 'none';
            }

            // Only show first document if we're showing the preview section and have files
            if (show && uploadedFiles.length > 0 && currentFileIndex === -1) {
                currentFileIndex = 0;
                showDocument(currentFileIndex);
            }
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Hide document preview section initially
            updateDocumentPreviewVisibility(false);

            // Initialize template selector
            initializeCollapsibleSelector('templateSelectorHeader', 'templateSelectorContent');

            // Enable the view toggle when a document is loaded
            document.getElementById('viewToggle').disabled = false;
        });

        function initializeCollapsibleSelector(headerId, contentId) {
            const header = document.getElementById(headerId);
            const content = document.getElementById(contentId);

            if (!header || !content) {
                console.warn(`Could not find elements for selector: ${headerId}`);
                return;
            }

            const chevron = header.querySelector('.fa-chevron-down');
            if (!chevron) {
                console.warn(`Could not find chevron for selector: ${headerId}`);
                return;
            }

            header.addEventListener('click', () => {
                content.classList.toggle('expanded');
                chevron.style.transform = content.classList.contains('expanded') ? 'rotate(180deg)' : '';
            });
        }

        // Document selector functionality
        document.addEventListener('DOMContentLoaded', function() {
            const documentSelector = document.querySelector('.document-selector-trigger');
            const documentSelectorContent = document.querySelector('.document-selector-content');

            if (documentSelector && documentSelectorContent) {
                documentSelector.addEventListener('click', (e) => {
                    e.stopPropagation();
                    documentSelectorContent.classList.toggle('show');
                });

                // Close the selector when clicking outside
                document.addEventListener('click', (e) => {
                    if (!documentSelector.contains(e.target) && !documentSelectorContent.contains(e.target)) {
                        documentSelectorContent.classList.remove('show');
                    }
                });

                // Prevent closing when clicking inside the dropdown
                documentSelectorContent.addEventListener('click', (e) => {
                    e.stopPropagation();
                });
            }
        });

        // Update the document navigation code
        function updateDocumentNavigation() {
            const prevBtn = document.getElementById('prevDoc');
            const nextBtn = document.getElementById('nextDoc');
            const currentDocTitle = document.getElementById('currentDocTitle');

            if (prevBtn && nextBtn && currentDocTitle) {
                prevBtn.disabled = currentFileIndex <= 0;
                nextBtn.disabled = currentFileIndex >= uploadedFiles.length - 1;

                // Update document title
                currentDocTitle.textContent = `Document ${currentFileIndex + 1} of ${uploadedFiles.length}`;
            }
        }

        // Add these event listeners after your existing ones
        const uploadSection = document.getElementById('uploadSection');
        const uploadForm = document.getElementById('uploadForm');
        const fileInput = document.getElementById('pdfFiles');
        const fileList = document.getElementById('fileList');
        const uploadContent = document.querySelector('.upload-content');

        uploadSection.addEventListener('click', () => {
            fileInput.click();
        });

        uploadSection.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadSection.classList.add('drag-over');
        });

        uploadSection.addEventListener('dragleave', (e) => {
            const rect = uploadSection.getBoundingClientRect();
            const x = e.clientX;
            const y = e.clientY;

            if (x <= rect.left || x >= rect.right || y <= rect.top || y >= rect.bottom) {
                uploadSection.classList.remove('drag-over');
            }
        });

        uploadSection.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadSection.classList.remove('drag-over');

            const files = Array.from(e.dataTransfer.files).filter(file =>
                file.type === 'application/pdf'
            ).slice(0, 10);

            if (files.length === 0) {
                showAlert('Please drop only PDF files (maximum 10)');
                return;
            }

            const dataTransfer = new DataTransfer();
            files.forEach(file => dataTransfer.items.add(file));
            fileInput.files = dataTransfer.files;

            // Submit the form automatically after files are dropped
            document.getElementById('uploadForm').dispatchEvent(new Event('submit'));
        });

        // Update the file list update function
        function updateFileList() {
            if (uploadedFiles.length > 0) {
                uploadContent.classList.add('hidden');
                fileList.classList.add('show');

                fileList.innerHTML = uploadedFiles.map(file => `
                    <div class="file-item" data-filename="${file.name}">
                        <i class="fas fa-file-pdf"></i>
                        <span class="file-name">${file.name}</span>
                        <span class="file-status ${fileStatuses[file.name] || ''}" style="margin-left: auto;">
                            ${getFileStatusText(file.name)}
                        </span>
                    </div>
                `).join('') + `
                <div class="add-more-files">
                    <i class="fas fa-plus-circle"></i>
                    <span>Add more files</span>
                </div>`;

                // Add click handlers for file items
                const fileItems = fileList.querySelectorAll('.file-item');
                fileItems.forEach(item => {
                    item.addEventListener('click', (e) => {
                        e.stopPropagation(); // Prevent upload section click
                        const fileName = item.dataset.filename;
                        const fileIndex = uploadedFiles.findIndex(f => f.name === fileName);
                        if (fileIndex !== -1) {
                            currentFileIndex = fileIndex;
                            showDocument(fileIndex);
                        }
                    });
                });

                // Add click handler for add more files button
                const addMoreBtn = fileList.querySelector('.add-more-files');
                if (addMoreBtn) {
                    addMoreBtn.addEventListener('click', (e) => {
                        e.stopPropagation(); // Prevent upload section click
                        fileInput.click();
                    });
                }
            } else {
                uploadContent.classList.remove('hidden');
                fileList.classList.remove('show');
            }
        }

        // Update the file status text helper
        function getFileStatusText(fileName) {
            const status = fileStatuses[fileName];
            switch (status) {
                case EXTRACTION_STATES.IN_PROGRESS:
                    return '<i class="fas fa-spinner fa-spin"></i> Extracting...';
                case EXTRACTION_STATES.COMPLETED:
                    return '<i class="fas fa-check"></i> Extracted';
                case EXTRACTION_STATES.ERROR:
                    return '<i class="fas fa-exclamation-circle"></i> Error';
                default:
                    return '<i class="fas fa-clock"></i> Not extracted';
            }
        }

        // Update the extract button handler
        document.getElementById('extractBtn').addEventListener('click', async function() {
            if (uploadedFiles.length === 0) return;

            // Check if there are any files that haven't been extracted yet
            const filesToExtract = uploadedFiles.filter(file =>
                !fileStatuses[file.name] ||
                fileStatuses[file.name] === EXTRACTION_STATES.NOT_STARTED ||
                fileStatuses[file.name] === EXTRACTION_STATES.ERROR
            );

            if (filesToExtract.length === 0) {
                showAlert('All documents have already been processed');
                return;
            }

            this.disabled = true;

            try {
                // Extract text only for files that haven't been extracted
                const extractionPromises = filesToExtract.map(async (file) => {
                    fileStatuses[file.name] = EXTRACTION_STATES.IN_PROGRESS;
                    updateFileList();

                    // Update the status in the Recent Documents view as well
                    updateRecentDocumentStatus(file.name, EXTRACTION_STATES.IN_PROGRESS);

                    try {
                        const response = await fetch('/extract_text', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                file_path: file.name
                            })
                        });

                        const data = await response.json();

                        if (response.ok) {
                            extractedTexts[file.name] = data.extracted_text;
                            fileStatuses[file.name] = EXTRACTION_STATES.COMPLETED;

                            // Update document_tracking.csv
                            await updateDocumentTracking(file.name, EXTRACTION_STATES.COMPLETED, data.extracted_text);
                        } else {
                            console.error('Error extracting text for', file.name, ':', data.error);
                            fileStatuses[file.name] = EXTRACTION_STATES.ERROR;

                            // Update document_tracking.csv
                            await updateDocumentTracking(file.name, EXTRACTION_STATES.ERROR, '');
                        }
                    } catch (error) {
                        console.error('Error extracting text for', file.name, ':', error);
                        fileStatuses[file.name] = EXTRACTION_STATES.ERROR;

                        // Update document_tracking.csv
                        await updateDocumentTracking(file.name, EXTRACTION_STATES.ERROR, '');
                    }

                    // Update UI after each file completes
                    updateFileList();
                    updateRecentDocumentStatus(file.name, fileStatuses[file.name]);
                    updateViewToggleState();
                });

                // Wait for all extractions to complete
                await Promise.all(extractionPromises);

                // Check if all files are now processed
                const unprocessedFiles = uploadedFiles.filter(file =>
                    !fileStatuses[file.name] ||
                    fileStatuses[file.name] === EXTRACTION_STATES.NOT_STARTED ||
                    fileStatuses[file.name] === EXTRACTION_STATES.ERROR
                );

                if (unprocessedFiles.length === 0) {
                    this.disabled = true;
                }

            } catch (error) {
                console.error('Error during batch extraction:', error);
                showAlert('An error occurred while extracting text from files');
            }

            this.disabled = false;
        });

        // Add event listener for the save button
        document.getElementById('saveTextBtn').addEventListener('click', async function() {
            if (!currentFile) return;

            const extractedTextView = document.getElementById('extractedTextView');
            const editedText = extractedTextView.value;

            // Save the edited text
            extractedTexts[currentFile.name] = editedText;

            // Update the document tracking CSV
            await updateDocumentTracking(currentFile.name, 'completed', editedText);

            // Show success message
            showAlert('Text saved successfully', 'success');
        });

        // Function to update document tracking in CSV
        async function updateDocumentTracking(fileName, status, extractedText) {
            try {
                const response = await fetch('/update_document_tracking', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        file_name: fileName,
                        status: status,
                        extracted_text: extractedText
                    })
                });

                if (!response.ok) {
                    const data = await response.json();
                    console.error('Error updating document tracking:', data.error);
                }
            } catch (error) {
                console.error('Error updating document tracking:', error);
            }
        }

        // Function to update status in the Recent Documents view
        function updateRecentDocumentStatus(fileName, status) {
            const recentItems = document.querySelectorAll('.recent-document-item');
            recentItems.forEach(item => {
                const fileNameElement = item.querySelector('.file-name');
                if (fileNameElement && fileNameElement.textContent === fileName) {
                    const statusElement = item.querySelector('.file-status');
                    if (statusElement) {
                        statusElement.className = 'file-status';

                        // Update class based on status
                        if (status === EXTRACTION_STATES.COMPLETED) {
                            statusElement.classList.add('processed');
                            statusElement.innerHTML = '<i class="fas fa-check"></i> Processed';
                        } else if (status === EXTRACTION_STATES.IN_PROGRESS) {
                            statusElement.classList.add('processing');
                            statusElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                        } else if (status === EXTRACTION_STATES.ERROR) {
                            statusElement.classList.add('error');
                            statusElement.innerHTML = '<i class="fas fa-exclamation-circle"></i> Error';
                        } else {
                            statusElement.classList.add('not-processed');
                            statusElement.innerHTML = '<i class="fas fa-clock"></i> Not Processed';
                        }
                    }
                }
            });
        }

        // Function to load recent documents
        async function loadRecentDocuments() {
            try {
                // Fetch both the list of files from the directory and the document tracking data
                const [filesResponse, trackingResponse] = await Promise.all([
                    fetch('/list_files'),
                    fetch('/get_document_tracking')
                ]);

                const filesData = await filesResponse.json();
                const trackingData = await trackingResponse.json();

                // Create a mapping of file names to their tracking data
                const trackingMap = {};
                trackingData.documents.forEach(doc => {
                    trackingMap[doc.file_name] = {
                        status: doc.extraction_status,
                        extractedText: doc.extracted_text,
                        lastUsed: doc.last_used
                    };

                    // Update our local status tracking
                    if (doc.extraction_status === 'completed') {
                        fileStatuses[doc.file_name] = EXTRACTION_STATES.COMPLETED;
                        if (doc.extracted_text) {
                            extractedTexts[doc.file_name] = doc.extracted_text;
                        }
                    } else if (doc.extraction_status === 'error') {
                        fileStatuses[doc.file_name] = EXTRACTION_STATES.ERROR;
                    }
                });

                const recentList = document.getElementById('recentDocumentsList');
                recentList.innerHTML = '';

                // Combine files from directory with tracking data
                let allFiles = filesData.files.map(file => {
                    // Check if we have tracking data for this file
                    const tracking = trackingMap[file.filename] || trackingMap['uploads/' + file.filename];

                    return {
                        filename: file.filename,
                        upload_time: file.upload_time,
                        isProcessed: tracking && tracking.status === 'completed',
                        isError: tracking && tracking.status === 'error',
                        extractedText: tracking ? tracking.extractedText : null,
                        lastUsed: tracking ? tracking.lastUsed : file.upload_time
                    };
                });

                // Add any tracked files that might not be in the directory anymore
                Object.keys(trackingMap).forEach(filename => {
                    const shortName = filename.replace('uploads/', '');
                    if (!allFiles.some(f => f.filename === shortName || f.filename === filename)) {
                        allFiles.push({
                            filename: shortName,
                            upload_time: trackingMap[filename].lastUsed || new Date().toISOString(),
                            isProcessed: trackingMap[filename].status === 'completed',
                            isError: trackingMap[filename].status === 'error',
                            extractedText: trackingMap[filename].extractedText,
                            lastUsed: trackingMap[filename].lastUsed
                        });
                    }
                });

                // Sort files by last used time (most recent first)
                allFiles = allFiles.sort((a, b) =>
                    new Date(b.lastUsed || b.upload_time) - new Date(a.lastUsed || a.upload_time)
                ).slice(0, 10);  // Get only the 10 most recent files

                allFiles.forEach(file => {
                    const isCurrent = uploadedFiles.some(f => f.name === file.filename);
                    const isProcessed = file.isProcessed || fileStatuses[file.filename] === EXTRACTION_STATES.COMPLETED;
                    const isError = file.isError || fileStatuses[file.filename] === EXTRACTION_STATES.ERROR;
                    const isProcessing = fileStatuses[file.filename] === EXTRACTION_STATES.IN_PROGRESS;

                    // Store extracted text if available
                    if (file.extractedText && !extractedTexts[file.filename]) {
                        extractedTexts[file.filename] = file.extractedText;
                    }

                    const docItem = document.createElement('div');
                    docItem.className = 'recent-document-item';

                    let statusHTML = '';
                    if (isProcessing) {
                        statusHTML = `
                            <div class="file-status processing">
                                <i class="fas fa-spinner fa-spin"></i> Processing...
                            </div>
                        `;
                    } else if (isProcessed) {
                        statusHTML = `
                            <div class="file-status processed">
                                <i class="fas fa-check"></i> Processed
                            </div>
                        `;
                    } else if (isError) {
                        statusHTML = `
                            <div class="file-status error">
                                <i class="fas fa-exclamation-circle"></i> Error
                            </div>
                        `;
                    } else {
                        statusHTML = `
                            <div class="file-status not-processed">
                                <i class="fas fa-clock"></i> Not Processed
                            </div>
                        `;
                    }

                    docItem.innerHTML = `
                        <div class="file-info">
                            <div class="file-name">${file.filename}</div>
                            <div class="file-meta">Last used: ${new Date(file.lastUsed || file.upload_time).toLocaleDateString()}</div>
                        </div>
                        ${statusHTML}
                        <button class="add-btn ${isCurrent ? 'added' : ''}" ${isCurrent ? 'disabled' : ''}>
                            ${isCurrent ? 'Added' : 'Add'}
                        </button>
                        ${isCurrent ? '<button class="remove-btn"><i class="fas fa-times"></i></button>' : ''}
                    `;

                    // Add click handler for the add button
                    const addBtn = docItem.querySelector('.add-btn');
                    if (!isCurrent) {
                        addBtn.addEventListener('click', () => addDocumentToCurrent(file));
                    }

                    // Add click handler for the remove button
                    const removeBtn = docItem.querySelector('.remove-btn');
                    if (removeBtn) {
                        removeBtn.addEventListener('click', (e) => {
                            e.stopPropagation();
                            removeDocumentFromCurrent(file);
                        });
                    }

                    recentList.appendChild(docItem);
                });

                // Show document preview section if any files are selected
                updateDocumentPreviewVisibility(uploadedFiles.length > 0);

                // Update extract button state
                updateExtractButtonState();
            } catch (error) {
                console.error('Error loading recent documents:', error);
                showAlert('Failed to load recent documents');
            }
        }

        // Add view toggle state update function
        function updateViewToggleState() {
            const currentFile = uploadedFiles[currentFileIndex];
            const viewToggleContainer = document.querySelector('.view-toggle-container');
            const viewToggle = document.getElementById('viewToggle');

            if (currentFile && extractedTexts[currentFile.name]) {
                viewToggleContainer.classList.remove('disabled');
                viewToggle.disabled = false;
            } else {
                viewToggleContainer.classList.add('disabled');
                viewToggle.disabled = true;
                viewToggle.checked = false;
            }
        }

        // Alert management
        function showAlert(message, type = 'error') {
            const alertContainer = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;

            // Set icon based on alert type
            let icon = 'exclamation-circle';
            if (type === 'success') icon = 'check-circle';
            else if (type === 'warning') icon = 'exclamation-triangle';
            else if (type === 'info') icon = 'info-circle';

            alert.innerHTML = `
                <i class="fas fa-${icon}"></i>
                <span>${message}</span>
                <i class="fas fa-times close-btn"></i>
            `;

            // Add close button handler
            const closeBtn = alert.querySelector('.close-btn');
            closeBtn.addEventListener('click', () => removeAlert(alert));

            // Add to container
            alertContainer.appendChild(alert);

            // Set timeout for auto-removal
            setTimeout(() => removeAlert(alert), 5000);
        }

        function removeAlert(alert) {
            if (!alert.parentElement) return;

            alert.style.animation = 'slideOut 0.3s ease-out';
            setTimeout(() => {
                if (alert.parentElement) {
                    alert.parentElement.removeChild(alert);
                }
            }, 300);
        }

        // Add tab system functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tabs
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const tabId = button.dataset.tab;

                    // Update active states
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    button.classList.add('active');
                    document.getElementById(`${tabId}Tab`).classList.add('active');

                    // Load recent documents if switching to recent tab
                    if (tabId === 'recent') {
                        loadRecentDocuments();
                    }
                });
            });

            // Load recent documents initially
            loadRecentDocuments();
        });

        // Function to add a document to current selection
        async function addDocumentToCurrent(file) {
            try {
                // Add to uploaded files array
                uploadedFiles.push({
                    name: file.filename,
                    url: `/serve_file/${file.filename}`,
                    upload_time: file.upload_time
                });

                // Update UI
                updateFileList();
                updateDocumentList();

                // Show the document
                currentFileIndex = uploadedFiles.length - 1;
                showDocument(currentFileIndex);

                // Refresh the recent documents list
                loadRecentDocuments();

                // Show success message
                showAlert('Document added successfully');
            } catch (error) {
                console.error('Error adding document:', error);
                showAlert('Failed to add document');
            }
        }

        // Function to remove a document from current selection
        function removeDocumentFromCurrent(file) {
            const index = uploadedFiles.findIndex(f => f.name === file.filename);
            if (index !== -1) {
                uploadedFiles.splice(index, 1);
                delete fileStatuses[file.filename];

                // Update UI
                updateFileList();
                updateDocumentList();

                // Update current file index
                if (uploadedFiles.length === 0) {
                    currentFileIndex = -1;
                    updateDocumentPreviewVisibility(false);
                } else {
                    currentFileIndex = Math.min(currentFileIndex, uploadedFiles.length - 1);
                    showDocument(currentFileIndex);
                }

                // Refresh the recent documents list
                loadRecentDocuments();

                // Show success message
                showAlert('Document removed successfully');
            }
        }

        // Function to update extract button state
        function updateExtractButtonState() {
            const extractBtn = document.getElementById('extractBtn');
            const hasUnprocessedFiles = uploadedFiles.some(file =>
                !fileStatuses[file.name] ||
                fileStatuses[file.name] === EXTRACTION_STATES.NOT_STARTED ||
                fileStatuses[file.name] === EXTRACTION_STATES.ERROR
            );

            extractBtn.disabled = !hasUnprocessedFiles || uploadedFiles.length === 0;
        }
    </script>
</body>
</html>
