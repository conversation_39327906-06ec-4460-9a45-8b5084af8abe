[{"id": 1, "name": "Auto Insurance", "description": "For extracting auto insurance policy details", "fields": ["policy_number", "customer_name", "vehicle_make", "vehicle_model", "coverage_amount"]}, {"id": 2, "name": "Home Insurance", "description": "For extracting home insurance policy details", "fields": ["policy_number", "customer_name", "property_address", "coverage_amount", "deductible"]}, {"id": 3, "name": "Life Insurance", "description": "For extracting life insurance policy details", "fields": ["policy_number", "customer_name", "beneficiary", "coverage_amount", "term_length"]}]