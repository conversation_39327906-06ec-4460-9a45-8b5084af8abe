{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Test"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["zsh:1: no matches found: amazon-textract-textractor[pdfium]\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install boto3 amazon-textract-textractor[pdfium]"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["'1.37.19'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import boto3\n", "\n", "boto3.__version__"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["#!/usr/bin/env python3\n", "\"\"\"\n", "AWS Bedrock Data Automation processing script for document analysis.\n", "This script processes documents using AWS Bedrock Data Automation and extracts specific fields.\n", "\"\"\"\n", "import json\n", "import sys\n", "import time\n", "from typing import Dict, Any, List, Optional\n", "import argparse\n", "\n", "import boto3\n", "from botocore.client import BaseClient\n", "\n", "\n", "class BedrockDataAutomation:\n", "    \"\"\"\n", "    A class to handle AWS Bedrock Data Automation for document analysis.\n", "    \"\"\"\n", "    # Configuration constants\n", "    VERBOSE_LOGGING = True\n", "    AWS_REGION = '<REGION>'\n", "    STORAGE_BUCKET = '<BUCKET>'\n", "    DOCUMENT_INPUT_DIR = 'Input'\n", "    DOCUMENT_OUTPUT_DIR = 'Output'\n", "    \n", "    AUTOMATION_PROJECT_ID = '<PROJECT_ID>'\n", "\n", "\n", "    def __init__(self):\n", "        \"\"\"Initialize AWS clients and other parameters.\"\"\"\n", "        self.bedrock_client = boto3.client('bedrock-data-automation-runtime', region_name=self.AWS_REGION)\n", "        self.storage_client = boto3.client('s3', region_name=self.AWS_REGION)\n", "        self.identity_client = boto3.client('sts')\n", "        self.account_id = self._retrieve_aws_account_id()\n", "\n", "    def _retrieve_aws_account_id(self) -> str:\n", "        \"\"\"\n", "        Get the current AWS account ID.\n", "        \n", "        Returns:\n", "            str: The AWS account ID\n", "        \"\"\"\n", "        return self.identity_client.get_caller_identity().get('Account')\n", "\n", "    def debug_log(self, data: Any) -> None:\n", "        \"\"\"\n", "        Log detailed information when verbose logging is enabled.\n", "        \n", "        Args:\n", "            data: The data to log (dictionary or other object)\n", "        \"\"\"\n", "        if not self.VERBOSE_LOGGING:\n", "            return\n", "            \n", "        if isinstance(data, dict):\n", "            formatted_text = json.dumps(data, indent=4)\n", "        else:\n", "            formatted_text = str(data)\n", "        print(formatted_text)\n", "\n", "    def fetch_json_from_s3(self, s3_uri: str) -> Dict[str, Any]:\n", "        \"\"\"\n", "        Download and parse a JSON object from S3.\n", "        \n", "        Args:\n", "            s3_uri: The S3 URI of the JSON object\n", "            \n", "        Returns:\n", "            dict: The parsed JSON content\n", "        \"\"\"\n", "        uri_parts = s3_uri.split('/')\n", "        bucket_name = uri_parts[2]\n", "        object_key = '/'.join(uri_parts[3:])\n", "        \n", "        response = self.storage_client.get_object(Bucket=bucket_name, Key=object_key)\n", "        content = response['Body'].read()\n", "        return json.loads(content)\n", "\n", "    def start_automation_job(self, input_uri: str, output_uri: str) -> Dict[str, Any]:\n", "        \"\"\"\n", "        Start an asynchronous Bedrock Data Automation job.\n", "        \n", "        Args:\n", "            input_uri: S3 URI for the input file\n", "            output_uri: S3 URI for the output directory\n", "            \n", "        Returns:\n", "            dict: Response from the API call\n", "        \"\"\"\n", "        automation_arn = f\"arn:aws:bedrock:{self.AWS_REGION}:{self.account_id}:data-automation-project/{self.AUTOMATION_PROJECT_ID}\"\n", "        \n", "        request_params = {\n", "            'inputConfiguration': {\n", "                's3Uri': input_uri\n", "            },\n", "            'outputConfiguration': {\n", "                's3Uri': output_uri\n", "            },\n", "            'dataAutomationConfiguration': {\n", "                'dataAutomationProjectArn': automation_arn\n", "            },\n", "            'dataAutomationProfileArn': f\"arn:aws:bedrock:{self.AWS_REGION}:{self.account_id}:data-automation-profile/us.data-automation-v1\"\n", "        }\n", "\n", "        response = self.bedrock_client.invoke_data_automation_async(**request_params)\n", "        self.debug_log(response)\n", "        return response\n", "\n", "    def monitor_automation_job(self, invocation_arn: str, poll_interval: int = 1) -> Dict[str, Any]:\n", "        \"\"\"\n", "        Poll and wait for a data automation job to complete.\n", "        \n", "        Args:\n", "            invocation_arn: The ARN of the job invocation\n", "            poll_interval: How often to check the status (in seconds)\n", "            \n", "        Returns:\n", "            dict: The final job status response\n", "        \"\"\"\n", "        while True:\n", "            status_response = self.bedrock_client.get_data_automation_status(\n", "                invocationArn=invocation_arn\n", "            )\n", "            current_status = status_response['status']\n", "            \n", "            if current_status not in ['Created', 'InProgress']:\n", "                print(f\" {current_status}\")\n", "                return status_response\n", "                \n", "            print(\".\", end='', flush=True)\n", "            time.sleep(poll_interval)\n", "\n", "    def display_document_analysis(self, result: Dict[str, Any]) -> None:\n", "        \"\"\"\n", "        Display the document analysis results.\n", "        \n", "        Args:\n", "            result: The document analysis data\n", "        \"\"\"\n", "        print(f\"Number of pages: {result['metadata']['number_of_pages']}\")\n", "        \n", "        for page in result['pages']:\n", "            print(f\"- Page {page['page_index']}\")\n", "            \n", "            if 'text' in page['representation']:\n", "                print(f\"{page['representation']['text']}\")\n", "                \n", "            if 'markdown' in page['representation']:\n", "                print(f\"{page['representation']['markdown']}\")\n", "\n", "\n", "    # def display_field_value(self, field_path: str, custom_result: Dict[str, Any]) -> None:\n", "    #     \"\"\"\n", "    #     Display a specific field value and its confidence.\n", "        \n", "    #     Args:\n", "    #         field_path: Path to the field, possibly containing a group path\n", "    #         custom_result: The custom extraction result\n", "    #     \"\"\"\n", "    #     inference = custom_result['inference_result']\n", "    #     confidence_data = custom_result['explainability_info'][0]\n", "        \n", "    #     if '/' in field_path:\n", "    #         # Handle fields within groups\n", "    #         group_name, field_name = field_path.split('/')\n", "    #         inference = inference[group_name]\n", "    #         confidence_data = confidence_data[group_name]\n", "    #     else:\n", "    #         field_name = field_path\n", "            \n", "    #     field_value = inference[field_name]\n", "    #     confidence_score = confidence_data[field_name]['confidence']\n", "        \n", "    #     display_value = field_value if field_value else '<EMPTY>'\n", "    #     print(f'{field_name}: {display_value}  Confidence: {confidence_score}')\n", "\n", "    # def display_extracted_fields(self, custom_result: Dict[str, Any]) -> None:\n", "    #     \"\"\"\n", "    #     Display the custom extraction results.\n", "        \n", "    #     Args:\n", "    #         custom_result: The custom field extraction data\n", "    #     \"\"\"\n", "    #     matched_blueprint = custom_result['matched_blueprint']['name']\n", "    #     self.debug_log(custom_result)\n", "        \n", "    #     print('\\n- Custom output')\n", "    #     print(f\"Matched blueprint: {matched_blueprint}  Confidence: {custom_result['matched_blueprint']['confidence']}\")\n", "    #     print(f\"Document class: {custom_result['document_class']['type']}\")\n", "        \n", "    #     if matched_blueprint == self.BLUEPRINT:\n", "    #         print('\\n- Fields')\n", "    #         for field_path in self.TARGET_FIELDS:\n", "    #             self.display_field_value(field_path, custom_result)\n", "\n", "    def display_job_results(self, metadata_uri: str) -> None:\n", "        \"\"\"\n", "        Display the complete results from a data automation job.\n", "        \n", "        Args:\n", "            metadata_uri: S3 URI of the job metadata file\n", "        \"\"\"\n", "        job_data = self.fetch_json_from_s3(metadata_uri)\n", "        self.debug_log(job_data)\n", "\n", "        for segment in job_data['output_metadata']:\n", "            asset_id = segment['asset_id']\n", "            print(f'\\nAsset ID: {asset_id}')\n", "\n", "            for segment_data in segment['segment_metadata']:\n", "                # Process standard output\n", "                standard_output_path = segment_data['standard_output_path']\n", "                standard_result = self.fetch_json_from_s3(standard_output_path)\n", "                self.debug_log(standard_result)\n", "                \n", "                print('\\n- Standard output')\n", "                content_type = standard_result['metadata']['semantic_modality']\n", "                print(f\"Semantic modality: {content_type}\")\n", "                \n", "                if content_type == 'DOCUMENT':\n", "                    self.display_document_analysis(standard_result)\n", "                    \n", "                # # Process custom output if available\n", "                # if 'custom_output_status' in segment_data and segment_data['custom_output_status'] == 'MATCH':\n", "                #     custom_output_path = segment_data['custom_output_path']\n", "                #     custom_result = self.fetch_json_from_s3(custom_output_path)\n", "                #     self.display_extracted_fields(custom_result)\n", "\n", "    def process_document(self, document_filename: str) -> None:\n", "        \"\"\"\n", "        Process a document with Bedrock Data Automation.\n", "        \n", "        Args:\n", "            document_filename: The name of the document file to process\n", "        \"\"\"\n", "        input_uri = f\"s3://{self.STORAGE_BUCKET}/{self.DOCUMENT_INPUT_DIR}/{document_filename}\"\n", "        output_uri = f\"s3://{self.STORAGE_BUCKET}/{self.DOCUMENT_OUTPUT_DIR}\"\n", "\n", "        print(f\"Invoking Bedrock Data Automation for '{document_filename}'\", end='', flush=True)\n", "\n", "        # Launch and monitor job\n", "        job_response = self.start_automation_job(input_uri, output_uri)\n", "        job_status = self.monitor_automation_job(job_response['invocationArn'])\n", "\n", "        # Process results if successful\n", "        if job_status['status'] == 'Success':\n", "            result_metadata_uri = job_status['outputConfiguration']['s3Uri']\n", "            self.display_job_results(result_metadata_uri)\n", "        else:\n", "            print(f\"Job failed with status: {job_status['status']}\")\n", "            if 'error' in job_status:\n", "                print(f\"Error: {job_status['error']}\")\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "filename = \"kitaoka-resume.pdf\"\n", "# Initialize the data automation processor\n", "processor = BedrockDataAutomation()\n", "\n", "# Set verbose logging if specified\n", "if args.verbose:\n", "    processor.VERBOSE_LOGGING = True\n", "\n", "# Process the specified document\n", "processor.process_document(args.filename)\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}