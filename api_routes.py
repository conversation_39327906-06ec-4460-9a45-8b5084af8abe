from flask import Blueprint, request, jsonify
from models import User, Document, Template, Template<PERSON>ield, ExtractedData
import uuid
from datetime import datetime

# This will be initialized in app.py
api = Blueprint('api', __name__)

# Repositories will be set in app.py
user_repo = None
document_repo = None
template_repo = None
extracted_data_repo = None

# User API endpoints
@api.route('/users', methods=['GET'])
def list_users():
    """List all users"""
    try:
        users = user_repo.list_users()
        return jsonify([user.to_dict() for user in users])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api.route('/users/<user_id>', methods=['GET'])
def get_user(user_id):
    """Get a user by ID"""
    try:
        user = user_repo.get_user(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404
        return jsonify(user.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api.route('/users', methods=['POST'])
def create_user():
    """Create a new user"""
    try:
        data = request.json
        user = User(
            user_id=data.get('user_id', str(uuid.uuid4())),
            username=data.get('username'),
            email=data.get('email'),
            is_admin=data.get('is_admin', False)
        )
        user = user_repo.create_user(user)
        return jsonify(user.to_dict()), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api.route('/users/<user_id>', methods=['PUT'])
def update_user(user_id):
    """Update a user"""
    try:
        data = request.json
        user = user_repo.get_user(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Update user fields
        user.username = data.get('username', user.username)
        user.email = data.get('email', user.email)
        user.is_admin = data.get('is_admin', user.is_admin)
        
        user = user_repo.update_user(user)
        return jsonify(user.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api.route('/users/<user_id>', methods=['DELETE'])
def delete_user(user_id):
    """Delete a user"""
    try:
        success = user_repo.delete_user(user_id)
        if not success:
            return jsonify({'error': 'User not found'}), 404
        return '', 204
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Document API endpoints
@api.route('/documents', methods=['GET'])
def list_documents():
    """List all documents"""
    try:
        user_id = request.args.get('user_id')
        documents = document_repo.list_documents(user_id)
        return jsonify([doc.to_dict() for doc in documents])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api.route('/documents/<file_name>', methods=['GET'])
def get_document(file_name):
    """Get a document by file name"""
    try:
        document = document_repo.get_document(file_name)
        if not document:
            return jsonify({'error': 'Document not found'}), 404
        return jsonify(document.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api.route('/documents/<file_name>', methods=['PUT'])
def update_document(file_name):
    """Update a document"""
    try:
        data = request.json
        document = document_repo.get_document(file_name)
        if not document:
            return jsonify({'error': 'Document not found'}), 404
        
        # Update document fields
        if 'extraction_status' in data:
            document.extraction_status = data['extraction_status']
        if 'extracted_text' in data:
            document.extracted_text = data['extracted_text']
        
        document.last_used = datetime.now().isoformat()
        
        if document.extraction_status in ['completed', 'error']:
            document.extraction_date = datetime.now().isoformat()
        
        document = document_repo.update_document(document)
        return jsonify(document.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api.route('/documents/<file_name>', methods=['DELETE'])
def delete_document(file_name):
    """Delete a document"""
    try:
        success = document_repo.delete_document(file_name)
        if not success:
            return jsonify({'error': 'Document not found'}), 404
        return '', 204
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Template API endpoints
@api.route('/templates', methods=['GET'])
def list_templates():
    """List all templates"""
    try:
        user_id = request.args.get('user_id')
        templates = template_repo.list_templates(user_id)
        return jsonify([template.to_dict() for template in templates])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api.route('/templates/<template_id>', methods=['GET'])
def get_template(template_id):
    """Get a template by ID"""
    try:
        template = template_repo.get_template(template_id)
        if not template:
            return jsonify({'error': 'Template not found'}), 404
        return jsonify(template.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api.route('/templates', methods=['POST'])
def create_template():
    """Create a new template"""
    try:
        data = request.json
        
        # Create template fields
        fields = []
        for field_data in data.get('fields', []):
            if isinstance(field_data, dict):
                field = TemplateField(
                    name=field_data.get('name', ''),
                    type=field_data.get('type', 'text'),
                    description=field_data.get('description', ''),
                    required=field_data.get('required', False)
                )
            else:
                # Handle case where fields are just strings
                field = TemplateField(name=field_data)
            fields.append(field)
        
        template = Template(
            template_id=data.get('template_id', str(uuid.uuid4())),
            name=data.get('name', ''),
            description=data.get('description', ''),
            user_id=data.get('user_id', ''),
            fields=fields
        )
        
        template = template_repo.create_template(template)
        return jsonify(template.to_dict()), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api.route('/templates/<template_id>', methods=['PUT'])
def update_template(template_id):
    """Update a template"""
    try:
        data = request.json
        template = template_repo.get_template(template_id)
        if not template:
            return jsonify({'error': 'Template not found'}), 404
        
        # Update template fields
        template.name = data.get('name', template.name)
        template.description = data.get('description', template.description)
        template.updated_at = datetime.now().isoformat()
        
        # Update fields if provided
        if 'fields' in data:
            fields = []
            for field_data in data.get('fields', []):
                if isinstance(field_data, dict):
                    field = TemplateField(
                        name=field_data.get('name', ''),
                        type=field_data.get('type', 'text'),
                        description=field_data.get('description', ''),
                        required=field_data.get('required', False)
                    )
                else:
                    # Handle case where fields are just strings
                    field = TemplateField(name=field_data)
                fields.append(field)
            template.fields = fields
        
        template = template_repo.update_template(template)
        return jsonify(template.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api.route('/templates/<template_id>', methods=['DELETE'])
def delete_template(template_id):
    """Delete a template"""
    try:
        success = template_repo.delete_template(template_id)
        if not success:
            return jsonify({'error': 'Template not found'}), 404
        return '', 204
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Extracted Data API endpoints
@api.route('/extracted_data', methods=['GET'])
def list_extracted_data():
    """List all extracted data"""
    try:
        document_id = request.args.get('document_id')
        template_id = request.args.get('template_id')
        data_list = extracted_data_repo.list_extracted_data(document_id, template_id)
        return jsonify([data.to_dict() for data in data_list])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api.route('/extracted_data/<document_id>/<template_id>', methods=['GET'])
def get_extracted_data(document_id, template_id):
    """Get extracted data by document and template IDs"""
    try:
        data = extracted_data_repo.get_extracted_data(document_id, template_id)
        if not data:
            return jsonify({'error': 'Extracted data not found'}), 404
        return jsonify(data.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api.route('/extracted_data', methods=['POST'])
def create_extracted_data():
    """Create new extracted data"""
    try:
        data = request.json
        extracted_data = ExtractedData(
            document_id=data.get('document_id'),
            template_id=data.get('template_id'),
            user_id=data.get('user_id'),
            extracted_values=data.get('extracted_values', {})
        )
        
        extracted_data = extracted_data_repo.create_extracted_data(extracted_data)
        return jsonify(extracted_data.to_dict()), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api.route('/extracted_data/<document_id>/<template_id>', methods=['PUT'])
def update_extracted_data(document_id, template_id):
    """Update extracted data"""
    try:
        data = request.json
        extracted_data = extracted_data_repo.get_extracted_data(document_id, template_id)
        if not extracted_data:
            return jsonify({'error': 'Extracted data not found'}), 404
        
        # Update extracted values
        if 'extracted_values' in data:
            extracted_data.extracted_values = data['extracted_values']
        
        extracted_data.extraction_date = datetime.now().isoformat()
        
        extracted_data = extracted_data_repo.update_extracted_data(extracted_data)
        return jsonify(extracted_data.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api.route('/extracted_data/<document_id>/<template_id>', methods=['DELETE'])
def delete_extracted_data(document_id, template_id):
    """Delete extracted data"""
    try:
        success = extracted_data_repo.delete_extracted_data(document_id, template_id)
        if not success:
            return jsonify({'error': 'Extracted data not found'}), 404
        return '', 204
    except Exception as e:
        return jsonify({'error': str(e)}), 500
